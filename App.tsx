import './gesture-handler';
import React, { useEffect, useState } from 'react';
import { wrap as wrapWithSentry } from '@sentry/react-native';
import { StyleSheet, View } from 'react-native';
import MainNavigator from '~/navigation/MainNavigator';
import AuthNavigator from '~/navigation/AuthNavigator';
import { SentryService } from '~/services/SentryService';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { LogoWithText } from '~/assets/icons';
import { getDataFromRealm } from '~/db/realm';
import { navigationRef } from '~/navigation/navigationService';
import { RealmProvider } from '@realm/react';
import colors from '~/styles/colors';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { ToastProvider } from '~/components/toast/ToastProvider';
import theme from '~/styles/theme';
import { ThemeProvider } from '@rneui/themed';
import MapboxGL from '@rnmapbox/maps';
import CustomModal from '~/components/modals/CustomModal';
import { CloudSlashFilled } from '~/components/icons';
import { useIsOnline } from '~/hooks/useIsOnline';
import TokenService from '~/services/TokenService';
import { KeychainService } from '~/utils/keyChain';
import {
  initializeNotificationService,
  useOneSignalObservers,
} from '~/services/notifications/NotificationsService';

MapboxGL.setAccessToken(process.env.MAPBOX_PUBLIC_TOKEN as string);

if (__DEV__) {
  import('./ReactotronConfig');
}

const linking = {
  prefixes: ['com.rapidmedical.app://'],
  config: {
    screens: {
      Login: 'oauth',
    },
  },
};

const RootStack = createNativeStackNavigator();

SentryService.initialize();

function App(): React.JSX.Element {
  const [isLoggedIn, setLogin] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const [showOfflineModal, setShowOfflineModal] = useState(false);

  const { isOnline, isNetInfoReady } = useIsOnline();

  useEffect(() => {
    const checkIfUserIsLoggedIn = async () => {
      await KeychainService.clearKeychainOnReinstall();

      const hasValidTokens = await TokenService.initialize();
      const userId = await getDataFromRealm<string>('userId');
      setLogin(hasValidTokens && !!userId);

      if (hasValidTokens && userId) {
        global.userId = userId;
      }
      setLoading(false);
    };

    checkIfUserIsLoggedIn();
  }, []);

  useEffect(() => {
    if (!isNetInfoReady) return;

    if (!isOnline) {
      setShowOfflineModal(true);
    } else {
      setShowOfflineModal(false);
    }
  }, [isOnline, isNetInfoReady]);

  initializeNotificationService();
  useOneSignalObservers();

  if (isLoading) {
    return (
      <View style={styles.container}>
        <LogoWithText />
      </View>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <ToastProvider>
        <GestureHandlerRootView>
          <RealmProvider>
            <NavigationContainer linking={linking} ref={navigationRef}>
              {isNetInfoReady && showOfflineModal && (
                <CustomModal
                  testID="Root.OfflineModal"
                  isVisible
                  onClose={() => setShowOfflineModal(false)}
                  headerText="Offline"
                  descriptionText="You are offline. Data will sync when you are back online. Continue schedule as normal."
                  cancelButtonText="Close"
                  onCancelPress={() => setShowOfflineModal(false)}
                  headerIcon={<CloudSlashFilled width={48} height={48} />}
                />
              )}

              <RootStack.Navigator
                initialRouteName={isLoggedIn ? 'Main' : 'AuthStack'}
                screenOptions={{
                  headerShown: false,
                  gestureEnabled: false,
                }}>
                <RootStack.Screen name="AuthStack" component={AuthNavigator} />
                <RootStack.Screen name="Main" component={MainNavigator} />
              </RootStack.Navigator>
            </NavigationContainer>
          </RealmProvider>
        </GestureHandlerRootView>
      </ToastProvider>
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
});

export default wrapWithSentry(App);
