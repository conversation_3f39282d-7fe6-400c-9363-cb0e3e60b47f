import { useEffect, useCallback } from 'react';
import { getStopsByDate } from '~/db/realm/operations/stop.operations';
import { StopWithRelations } from '~/types/stops.types';
import { getRouteById, saveDataToRealm } from '~/db/realm';
import {
  setRouteAndStopId,
  startHeartbeat,
  stopHeartbeat,
} from '~/services/location/LocationService';

type UseHeartbeatLocationProps = {
  date?: string;
};

export const useHeartbeatLocation = ({ date }: UseHeartbeatLocationProps) => {
  /**
   * Fetches the next stop ID for a given date.
   */
  const getNextStop = useCallback(
    async (selectedDate: string): Promise<StopWithRelations | null> => {
      try {
        const stopList: StopWithRelations[] =
          await getStopsByDate(selectedDate);

        if (!stopList || stopList.length === 0) {
          console.info('No stops found for the given date:', selectedDate);
          return null;
        }

        // Perform sorting in a separate statement
        const sortedStops = [...stopList].sort((a, b) => {
          const timeA = a.Stop_Time_Preferred__c || ''; // Handle null values safely
          const timeB = b.Stop_Time_Preferred__c || '';

          return timeA.localeCompare(timeB);
        });

        // Return the ID of the first stop from the sorted list
        const nextStop = sortedStops[0] || null;

        return nextStop;
      } catch (error) {
        console.error('useHeartbeatLocation: getNextStop(): ', error);
        return null;
      }
    },
    [],
  );

  /**
   * Handles daily schedules that are in progress.
   */
  const handleHeartbeatForInProgressSchedule = useCallback(
    async (selectedDate: string) => {
      const latestStop = await getNextStop(selectedDate);

      if (!latestStop) {
        console.info(
          '[FINAL] No stop found for location tracking. Stopping heartbeat.',
        );
        stopHeartbeat();
        return;
      }

      const latestStopId = latestStop?.Id;
      const routeId = latestStop?.Summary__c;
      const dailyScheduleId = latestStop?.Daily_Schedule__c;

      const shouldUseDailySchedule =
        await determineShouldUseDailySchedule(routeId);

      if (shouldUseDailySchedule && dailyScheduleId) {
        await saveDataToRealm<string | null>(
          'dailyScheduleId',
          dailyScheduleId,
        );
        await saveDataToRealm<boolean>('lockCheckInOrder', false);
        setRouteAndStopId(routeId, latestStopId, dailyScheduleId, false);
      } else {
        await saveDataToRealm<string | null>('dailyScheduleId', null);
        await saveDataToRealm<boolean>('lockCheckInOrder', true);
        setRouteAndStopId(routeId, latestStopId, undefined, true);
      }

      console.info(
        `[FINAL] Going to start location tracking for Route id: ${routeId}, Stop id: ${latestStopId} for post date ${selectedDate}`,
      );
      startHeartbeat();

      await saveDataToRealm<string | null>('routeId', routeId ?? null);
      await saveDataToRealm<string | null>('stopId', latestStopId ?? null);
      await saveDataToRealm<string | null>('date', selectedDate ?? null);
    },
    [getNextStop],
  );

  const determineShouldUseDailySchedule = async (routeId: string | null) => {
    if (!routeId) {
      return { shouldUseDailySchedule: false };
    }

    try {
      const route = await getRouteById(routeId);
      return {
        shouldUseDailySchedule: route && !route.Lock_Check_In_Order__c,
      };
    } catch (error) {
      console.warn(
        `[HEARTBEAT] Failed to get route ${routeId}, defaulting to regular mode:`,
        error,
      );
      return { shouldUseDailySchedule: false };
    }
  };
  const handleTaskChange = useCallback(async () => {
    if (!date) return;

    await handleHeartbeatForInProgressSchedule(date);
  }, [date, handleHeartbeatForInProgressSchedule]);

  useEffect(() => {
    console.info('Date for location tracking: ', date);

    if (!date) return;

    const debounceTimer = setTimeout(() => {
      handleTaskChange();
    }, 5000); // Debounce duration: 5 seconds

    // Cleanup the timer if the dependency changes before 5 seconds
    return () => {
      clearTimeout(debounceTimer);
    };
  }, [handleTaskChange, date]);

  return { handleTaskChange };
};
