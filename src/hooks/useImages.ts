import { useQuery } from '@realm/react';
import { useMemo } from 'react';
import { ImageSchema } from '~/db/realm/schemas/image.schema';
import { ImageTitleType } from '~/services/ImageStoreService';
import { IImage, ImageInfo } from '~/types/image.types';
import { ImageTitleInfo } from '~/utils/images';

type UseImagesProps = {
  titleTypes: ImageTitleType[];
  stopId: string;
  routeSummaryId: string;
  parcelId?: string;
  refreshKey?: number | string;
};

type ImagesData = {
  [K in ImageTitleType]: K extends keyof typeof ImageTitleInfo
    ? (typeof ImageTitleInfo)[K]['multiple'] extends true
      ? ImageInfo[]
      : ImageInfo | undefined
    : never;
};

export const useImages = ({
  titleTypes,
  stopId,
  routeSummaryId,
  parcelId,
  refreshKey,
}: UseImagesProps) => {
  const imageRecords = useQuery<IImage>({
    type: ImageSchema.name,
    query: records => {
      let query = `StopId == $0 AND RouteSummaryId == $1`;
      const params: (string | undefined)[] = [stopId, routeSummaryId];

      if (parcelId) {
        query += ' AND ParcelId == $2';
        params.push(parcelId);
      }

      return records.filtered(query, ...params);
    },
  });

  const data = useMemo<ImagesData>(() => {
    refreshKey?.toString(); // ensure refreshKey triggers recompute

    const imagesData = {} as ImagesData;

    const groupedImages = new Map<string, IImage[]>();

    imageRecords.forEach(image => {
      const arr = groupedImages.get(image.Title) ?? [];
      arr.push(image);
      groupedImages.set(image.Title, arr);
    });

    titleTypes.forEach(titleType => {
      const { title, multiple } = ImageTitleInfo[titleType];
      const matchingImages = (groupedImages.get(title) ?? []).map(image => ({
        id: image.Id,
        uri: `data:image/jpeg;base64,${image.VersionData}`,
      }));

      if (multiple) {
        imagesData[titleType] = matchingImages;
      } else {
        imagesData[titleType] =
          matchingImages.length > 0 ? matchingImages[0] : undefined;
      }
    });

    return imagesData;
  }, [imageRecords, titleTypes, refreshKey]);

  return { data };
};
