import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React from 'react';
import SubHeader from '~/components/headers/SubHeader';
import { AddParcelProvider } from '~/screens/parcel/AddParcelProvider';
import ParcelInformationScreen from '~/screens/parcel/ParcelInformationScreen';
import ParcelProofOfServiceScreen from '~/screens/parcel/ParcelProofOfServiceScreen';
import EditParcelScreen from '~/screens/parcel/EditParcelScreen';
import SelectParcelTypeScreen from '~/screens/parcel/SelectParcelTypeScreen';
import { Stop, StopType, TaskListItem } from '~/types/stops.types';
import { RouteProp } from '@react-navigation/native';
import colors from '~/styles/colors';
import { validateThatDataIsNotEmpty } from '~/utils/validation/data';
import en from '~/localization/en';
import { Parcel } from '~/types/parcel.types';

export type RootStackParamList = {
  SelectParcelTypeScreen: {
    stop: Stop;
    stopList: Stop[];
    task: TaskListItem;
    numberOfServices: number;
    type: StopType;
  };
  ParcelInformationScreen: { type: StopType };
  ParcelProofOfServiceScreen: { type: StopType };
  EditParcelScreen: {
    stop: Stop;
    stopId: string;
    stopList: Stop[];
    parcel: Parcel;
    type: StopType;
  };
};

const Stack = createNativeStackNavigator<RootStackParamList>();

function ParcelStack({ route }: { route: any }) {
  const { params, stop, stopList, parcelTypes, stopType } = route.params ?? {};

  const finalStop = stop ?? params?.stop;
  const finalStopList = stopList ?? params?.stopList;
  const finalParcelTypes = parcelTypes ?? params?.parcelTypes;
  const finalType = stopType ?? params?.stopType;

  return (
    <AddParcelProvider
      stop={finalStop}
      stopList={finalStopList}
      parcelTypes={finalParcelTypes}
      stopType={finalType}>
      <Stack.Navigator
        initialRouteName="SelectParcelTypeScreen"
        screenOptions={{ headerShadowVisible: false }}>
        <Stack.Screen
          name="SelectParcelTypeScreen"
          component={SelectParcelTypeScreen}
          options={getScreenOptions('1')}
        />

        <Stack.Screen
          name="ParcelInformationScreen"
          component={ParcelInformationScreen}
          options={getScreenOptions('2')}
        />

        <Stack.Screen
          name="ParcelProofOfServiceScreen"
          component={ParcelProofOfServiceScreen}
          options={getScreenOptions('3')}
        />

        <Stack.Screen
          name="EditParcelScreen"
          component={EditParcelScreen}
          options={() => ({
            header: () => <SubHeader title={en.edit_parcel} />,
          })}
        />
      </Stack.Navigator>
    </AddParcelProvider>
  );
}

function getScreenOptions(screenNumber: string) {
  return ({
    route: routeData,
    options,
  }: {
    route: RouteProp<RootStackParamList, keyof RootStackParamList>;
    options: any;
  }) => ({
    header: () => (
      <SubHeader
        route={routeData}
        options={options}
        title={
          validateThatDataIsNotEmpty(routeData.params?.stop?.Type__c ?? '')
            ? (routeData.params?.stop?.Type__c ?? en.add_a_parcel)
            : en.add_a_parcel
        }
        showContactDispatch={false}
        showProgressText={true}
        progressHeaderText={`${screenNumber}/3`}
        backgroundColor={colors.backgroundLight}
      />
    ),
    headerBackVisible: false,
  });
}

export default ParcelStack;
