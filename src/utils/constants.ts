export const URLs = {
  PRIVACY_POLICY: 'https://www.gorapidmedical.com/terms/privacy-policy-mobile',
};

export const ERROR_CODES = {
  OK: 200,
  NO_RESPONSE: 204,
  UNAUTHORIZED: 401,
  SERVER_ERROR: 500,
};

export const IMAGE_ASPECT_RATIO = 0.33;

export const MAX_RETRY_COUNT = 3;

export const SCREENS_TO_EXCLUDE_FOR_BOTTOM_TABS = [
  'RouteDetailScreen',
  'CameraViewScreen',
  'PhotoViewScreen',
  'ParcelStack',
  'StopOverviewScreen',
  'ConfirmationScreen',
  'CollectSignature',
  'ProtocolEngineStack',
  'StopDetailScreen',
  'SelectServiceScreen',
  'MapNavigationScreen',
];

const ROUTE_STATUS = {
  Ready: 'Ready',
  InProgress: 'InProgress',
  Scheduled: 'Scheduled',
  Complete: 'Complete',
  Cancelled: 'Cancelled',
} as const;

const STOP_TYPE = {
  Delivery: 'Delivery',
  Pickup: 'Pickup',
  Exchange: 'Exchange',
  Meet: 'Meet',
  Flight: 'Flight',
  Start: 'Start',
  End: 'End',
  Break: 'Break',
  Service: 'Service',
} as const;

const STOP_STATUS = {
  SCHEDULED: 'SCHEDULED',
  ARRIVED: 'ARRIVED',
  COMPLETE: 'COMPLETE',
  INACTIVE: 'INACTIVE',
  LATE: 'LATE',
  OPEN: 'OPEN',
  ON_SCHEDULE: 'ON_SCHEDULE',
} as const;

export { ROUTE_STATUS, STOP_TYPE, STOP_STATUS };
