import { useMemo } from 'react';
import { Stop } from '~/types/stops.types';
import { getFormattedTimeWithoutTimeZone } from '~/utils/dateAndTime';

export const useDestinationsData = (stopList: Stop[], stopId: string) => {
  return useMemo(() => {
    return stopList
      .filter(
        item =>
          item.Id !== stopId &&
          item.Completed_Time__c === null &&
          item.Stop_Time_Preferred__c, // valid date required
      )
      .map(item => {
        const timestamp = new Date(item.Stop_Time_Preferred__c ?? '').getTime();
        return {
          id: item.Id,
          title: item.Name,
          subtitle: item.Address_1__c ?? '',
          rightText: getFormattedTimeWithoutTimeZone(
            item.Stop_Time_Preferred__c,
          ),
          timeStamp: isNaN(timestamp) ? 0 : timestamp,
        };
      })
      .sort((a, b) => a.timeStamp - b.timeStamp);
  }, [stopList, stopId]);
};

export const PARCEL_TYPES = [
  'Blue Bag (medical records)',
  'Blue Tote',
  'Boxes',
  'Drinks',
  'Food',
  'Frozen Specimen',
  'Generic',
  'Inter-Office Mail',
  'Medical equipment',
  'Medication',
  'Medications',
  'Other',
  'Red Tote',
  'Room Temperature',
  'Sealed Bag',
  'Snacks',
  'Specimens',
  'Sterile Green Tag',
  'Sterile Red Tag',
  'TPN',
  'USPS Mail',
  'Yellow Bag (medical records)',
];
