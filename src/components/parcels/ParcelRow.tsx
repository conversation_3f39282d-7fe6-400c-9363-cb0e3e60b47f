import React, { useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Pen, Trash } from '~/components/icons';
import { marginLeft10 } from '~/styles/spacing';
import { Parcel } from '~/types/parcel.types';
import colors from '~/styles/colors';
import { blackText } from '~/styles/text';
import en from '~/localization/en';
import CustomModal from '~/components/modals/CustomModal';
import { DustbinWithBackground } from '~/assets/icons';

interface ParcelRowProps {
  parcel: Parcel;
  isEditable: boolean;
  onEditClick: () => void;
  onDelete: (parcelId: string) => void;
  canBeDeleted: boolean;
}

const ParcelRow = ({
  parcel,
  isEditable,
  onEditClick,
  onDelete,
  canBeDeleted,
}: ParcelRowProps) => {
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);

  const handleDelete = async () => {
    onDelete(parcel.Id);
    setIsDeleteModalVisible(false);
  };

  return (
    <View key={parcel.Id} style={styles.parcelRow}>
      <View style={styles.parcelOuterRow}>
        <View style={styles.parcelInnerColumn}>
          <Text style={styles.text}>
            <Text style={styles.quantity}>{parcel.Quantity__c}</Text>{' '}
            {parcel.Parcel_Type_Name__c}
          </Text>

          {isEditable && (
            <TouchableOpacity style={marginLeft10} onPress={onEditClick}>
              <Pen />
            </TouchableOpacity>
          )}
        </View>
        {canBeDeleted && (
          <TouchableOpacity
            style={marginLeft10}
            onPress={() => setIsDeleteModalVisible(true)}>
            <Trash />
          </TouchableOpacity>
        )}
      </View>

      <CustomModal
        testID="delete-parcel-modal"
        isVisible={isDeleteModalVisible}
        headerIcon={<DustbinWithBackground />}
        onClose={() => setIsDeleteModalVisible(false)}
        headerText={en.delete_parcel
          .replace('{quantity}', String(parcel.Quantity__c ?? ''))
          .replace('{type}', parcel.Parcel_Type_Name__c ?? '')}
        descriptionText={en.delete_parcel_description}
        okButtonText={en.delete}
        cancelButtonText={en.cancel}
        onOkPress={handleDelete}
        onCancelPress={() => setIsDeleteModalVisible(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  parcelRow: {
    padding: 10,
    backgroundColor: colors.lightGrayishBlue,
    borderRadius: 10,
    marginVertical: 4,
  },
  parcelOuterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  parcelInnerColumn: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  text: {
    fontSize: 16,
    flexShrink: 1,
    flexGrow: 1,
    ...blackText,
  },
  quantity: {
    fontWeight: '500',
    marginRight: 5,
  },
});

export default ParcelRow;
