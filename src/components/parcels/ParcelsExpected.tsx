import {
  Text,
  TextStyle,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  ViewStyle,
  StyleSheet,
} from 'react-native';
import { buttonText, h4, h5 } from '~/styles/text';
import colors from '~/styles/colors';
import { marginBottom8, marginTop12, marginTop8 } from '~/styles/spacing';
import React from 'react';
import { ParcelWithBackground } from '~/assets/icons';
import en from '~/localization/en';
import { row, center } from '~/styles/views';
import ParcelRow from '~/components/parcels/ParcelRow';
import { Parcel } from '~/types/parcel.types';
import { StopType } from '~/types/stops.types';
import { ParcelManager } from '~/services/sync/parcelManager';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { ImageSchema } from '~/db/realm/schemas/image.schema';
import { safeWrite } from '~/db/realm/utils/safeRealm';

interface ParcelsExpectedProps {
  isLastStop: boolean;
  stopType: string;
  deliveryParcels: Parcel[];
  pickupParcels: Parcel[];
  navigateToAddParcelFlow: () => void;
  handleEditParcel: (parcel: Parcel) => void;
  onParcelDeleted: () => void;
}

const TitleRow = ({
  title,
  shouldShowAddButton,
  onClick,
}: {
  title: string;
  shouldShowAddButton: boolean;
  onClick: () => void;
}) => {
  return (
    <View style={[styles.row, marginBottom8]}>
      <Text style={styles.infoLabel}>{title}</Text>

      {shouldShowAddButton && (
        <TouchableOpacity style={marginTop8} onPress={onClick}>
          <Text style={styles.redTextButton}>{en.add_new}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const ParcelsExpected = ({
  stopType,
  deliveryParcels,
  pickupParcels,
  navigateToAddParcelFlow,
  handleEditParcel,
  onParcelDeleted,
}: ParcelsExpectedProps) => {
  const isPickup = stopType === StopType.Pickup || stopType === StopType.Start;
  const isDelivery =
    stopType === StopType.Delivery || stopType === StopType.End;

  const handleParcelDelete = async (parcelId: string) => {
    try {
      const result = await ParcelManager.deleteParcel(parcelId);
      if (!result.success) {
        console.error('ParcelRow: handleDelete():', result.message);
        return;
      }

      const realm = await getRealmInstance();

      if (!realm || realm.isClosed) return;
      safeWrite(realm, () => {
        const imagesToDelete = realm
          .objects(ImageSchema.name)
          .filtered('ParcelId == $0', parcelId);
        realm.delete(imagesToDelete);
      });

      onParcelDeleted?.();
    } catch (error) {
      console.error('Failed to delete parcel or associated images:', error);
    }
  };

  return (
    <>
      {isPickup && (
        <View>
          <TitleRow
            title={en.pickup_parcels}
            shouldShowAddButton={true}
            onClick={navigateToAddParcelFlow}
          />

          {pickupParcels.map(parcel => (
            <ParcelRow
              key={parcel.Id}
              parcel={parcel}
              isEditable={true}
              canBeDeleted={true}
              onEditClick={() => handleEditParcel(parcel)}
              onDelete={handleParcelDelete}
            />
          ))}

          {pickupParcels.length === 0 && (
            <TouchableWithoutFeedback
              style={marginTop12}
              onPress={navigateToAddParcelFlow}>
              <View style={styles.parcelPlaceholder}>
                <ParcelWithBackground />
                <Text style={styles.infoText}>{en.add_parcels_manually}</Text>
              </View>
            </TouchableWithoutFeedback>
          )}
        </View>
      )}

      {isDelivery && (
        <View style={marginTop8}>
          <TitleRow
            title={en.delivery_parcels}
            shouldShowAddButton={true}
            onClick={navigateToAddParcelFlow}
          />

          {deliveryParcels.map(parcel => (
            <ParcelRow
              key={parcel.Id}
              parcel={parcel}
              isEditable={parcel.Pickup__c === '' ? true : false}
              canBeDeleted={parcel.Pickup__c === '' ? true : false}
              onEditClick={() => handleEditParcel(parcel)}
              onDelete={handleParcelDelete}
            />
          ))}
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  infoLabel: {
    ...h5,
    color: colors.grey900,
  } as TextStyle,
  infoText: {
    ...h4,
    ...marginTop8,
    color: colors.grey900,
    fontSize: 16,
  } as TextStyle,
  row: {
    ...(row as ViewStyle),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  redTextButton: {
    ...(buttonText as TextStyle),
    color: colors.red400,
  } as TextStyle,
  parcelPlaceholder: {
    ...(center as ViewStyle),
    height: 120,
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
  },
});

export default ParcelsExpected;
