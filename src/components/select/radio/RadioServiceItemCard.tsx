import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ViewStyle,
  StyleSheet,
  TextStyle,
} from 'react-native';
import colors from '~/styles/colors';
import {
  letterSpacing,
  fontFamilyBasedOnRunningOS,
  blackText,
  heading,
  leftText,
  grayText,
  fontSize18,
  disabledText,
} from '~/styles/text';
import {
  column,
  container,
  radioButtonSelected,
  radioButtonUnselected,
  row,
} from '~/styles/views';
import { StopType, TaskListItem } from '~/types/stops.types';
import { Checkmark, CircleBackground } from '~/components/icons';
import {
  marginBottom10,
  marginRight12,
  marginTop18,
  marginTop16,
  marginTop8,
  gap8,
} from '~/styles/spacing';

const RadioServiceItemCard = ({
  item,
  isSelected,
  onItemSelected,
  isCompleted,
  allowSelectionWhenCompleted = true,
}: {
  item: TaskListItem;
  isSelected: boolean;
  onItemSelected: (id: string) => void;
  isCompleted: boolean;
  allowSelectionWhenCompleted?: boolean;
}) => {
  const radioButtonStyle = isSelected
    ? radioButtonSelected
    : radioButtonUnselected;

  return (
    <TouchableOpacity
      id={`RadioButtonItem.TouchableOpacity.${item.type}`}
      style={styles.listItem}
      onPress={() => {
        onItemSelected(item);
      }}
      disabled={allowSelectionWhenCompleted && isCompleted}>
      <View style={container}>
        <View style={styles.header}>
          <View style={[styles.columnView, marginRight12, marginTop8]}>
            <CircleBackground fillColor={colors.backgroundWhite}>
              {item.icon}
            </CircleBackground>
          </View>

          <View style={container}>
            <Text style={styles.title} numberOfLines={1}>
              {item.type === StopType.Service ? item.data?.Name : item.type}
            </Text>

            {Array.isArray(item.data) && item.data.length > 0 && (
              <View style={[container, gap8, marginTop16]}>
                {item.data.map(
                  data =>
                    data.Quantity__c &&
                    data.Parcel_Type_Name__c && (
                      <Text
                        key={`${data.Parcel_Type_Name__c}-${data.Quantity__c}`}
                        style={styles.subtitle}
                        numberOfLines={1}>
                        {`${data.Quantity__c} ${data.Parcel_Type_Name__c}`}
                      </Text>
                    ),
                )}
              </View>
            )}
          </View>

          {isCompleted ? (
            <View style={[styles.columnView, marginTop16]}>
              <Checkmark color={colors.greenDark} size={38} />
            </View>
          ) : (
            <View
              style={[
                radioButtonStyle as ViewStyle,
                styles.columnView,
                marginTop18,
              ]}
            />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    borderRadius: 8,
  },
  header: {
    ...row,
    ...marginBottom10,
    ...container,
    alignItems: 'center',
    gap: 8,
  } as ViewStyle,
  title: {
    ...container,
    ...heading,
    ...blackText,
    ...leftText,
    ...marginTop16,
    fontSize: 20,
  } as TextStyle,
  columnView: {
    ...column,
    justifyContent: 'center',
    alignSelf: 'flex-start',
  } as ViewStyle,
  subtitle: {
    ...letterSpacing,
    ...grayText,
    ...fontSize18,
    ...disabledText,
    fontFamily: fontFamilyBasedOnRunningOS,
  } as TextStyle,
});

export default RadioServiceItemCard;
