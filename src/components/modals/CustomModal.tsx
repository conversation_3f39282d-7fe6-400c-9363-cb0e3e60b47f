import React from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextStyle,
} from 'react-native';
import { BlurView } from '@react-native-community/blur';
import colors from '~/styles/colors';
import { h3 } from '~/styles/text';

interface CustomModalProps {
  isVisible: boolean;
  onClose: () => void;
  headerText: string;
  descriptionText: string;
  okButtonText?: string;
  cancelButtonText?: string;
  onOkPress?: () => void;
  onCancelPress?: () => void;
  headerIcon?: any;
  testID?: string;
  disabled?: boolean;
}

/**
 * A custom modal component that displays a header, description, and optional action buttons.
 *
 * @component
 * @param {boolean} props.isVisible - Controls the visibility of the modal. `true` to show, `false` to hide.
 * @param {function} props.onClose - Callback function to close the modal. Triggered on modal dismiss or cancel action.
 * @param {string} props.headerText - The main title text displayed at the top of the modal.
 * @param {string} props.headerIcon - The main icon displayed at the top of the modal.
 * @param {string} props.descriptionText - The body text displayed as the modal's main content.
 * @param {string} [props.okButtonText] - Optional. Text displayed on the OK/confirmation button. Defaults to "OK" if not provided.
 * @param {string} [props.cancelButtonText] - Optional. Text displayed on the cancel button. Defaults to "Cancel" if not provided.
 * @param {function} [props.onOkPress] - Optional. Callback function triggered when the OK button is pressed.
 * @param {function} [props.onCancelPress] - Optional. Callback function triggered when the Cancel button is pressed.
 * @param {string} [props.testID] - Optional. TestID for the modal.
 *
 * @example
 * // Example usage of CustomModal
 * <CustomModal
 *   isVisible={isModalVisible}
 *   onClose={() => setIsModalVisible(false)}
 *   headerText="Delete Item"
 *   descriptionText="Are you sure you want to delete this item?"
 *   okButtonText="Yes"
 *   cancelButtonText="No"
 *   onOkPress={handleDelete}
 *   onCancelPress={() => setIsModalVisible(false)}
 * />
 */
const CustomModal: React.FC<CustomModalProps> = ({
  isVisible,
  onClose,
  headerText,
  descriptionText,
  okButtonText,
  cancelButtonText,
  onOkPress,
  onCancelPress,
  headerIcon,
  testID,
  disabled,
}) => {
  const isOnlyOkButtonVisible = okButtonText && !cancelButtonText;
  const isOnlyCancelButtonVisible = cancelButtonText && !okButtonText;
  const isBottomViewVisible = okButtonText || cancelButtonText;

  const renderBottomActionView = () => {
    return (
      <View style={styles.bottomView}>
        {cancelButtonText && (
          <TouchableOpacity
            style={[
              styles.button,
              styles.cancelButton,
              isOnlyCancelButtonVisible && styles.flexOne,
            ]}
            aria-disabled={disabled}
            disabled={disabled}
            onPress={onCancelPress}>
            <Text style={styles.cancelButtonText}>{cancelButtonText}</Text>
          </TouchableOpacity>
        )}
        {okButtonText && (
          <TouchableOpacity
            style={[
              styles.button,
              styles.okButton,
              isOnlyOkButtonVisible && styles.flexOne,
            ]}
            aria-disabled={disabled}
            disabled={disabled}
            onPress={onOkPress}>
            <Text style={styles.okButtonText}>{okButtonText}</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <Modal
      testID={testID}
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent>
      <BlurView style={styles.blurView} blurType="dark" blurAmount={10} />
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <View style={styles.headerView}>
            {headerIcon}
            <Text style={styles.headerText}>{headerText}</Text>
          </View>
          <View style={styles.descriptionView}>
            <Text style={styles.descriptionText}>{descriptionText}</Text>
          </View>
          {isBottomViewVisible && renderBottomActionView()}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  blurView: {
    ...StyleSheet.absoluteFillObject,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalView: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    overflow: 'hidden',
  },
  headerView: {
    alignItems: 'center',
    marginBottom: 10,
    flexDirection: 'column',
    paddingTop: 16,
    paddingHorizontal: 16,
  },
  headerText: {
    ...h3,
    fontSize: 20,
    color: colors.modalHeader,
    marginBottom: 8,
    marginTop: 16,
    textAlign: 'center',
  } as TextStyle,
  descriptionView: {
    marginBottom: 15,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  descriptionText: {
    ...h3,
    fontSize: 16,
    color: colors.modalDesc,
    textAlign: 'center',
    lineHeight: 22,
  } as TextStyle,
  bottomView: {
    flexDirection: 'row',
    backgroundColor: colors.blue300,
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  button: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    marginHorizontal: 5,
    borderRadius: 8,
  },
  okButton: {
    backgroundColor: colors.red600,
  },
  okButtonText: {
    ...h3,
    fontSize: 16,
    color: colors.white,
    textAlign: 'center',
  } as TextStyle,
  cancelButton: {
    backgroundColor: 'white',
    borderColor: colors.lightGray,
    borderWidth: 1,
  },
  cancelButtonText: {
    ...h3,
    fontSize: 16,
    color: colors.grey900,
    textAlign: 'center',
  } as TextStyle,
  flexOne: {
    flex: 1,
  },
});

export default CustomModal;
