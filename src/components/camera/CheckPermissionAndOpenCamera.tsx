import { CameraPermissionService } from '~/services/permission/CameraPermissionService';

// Function to check camera permissions and navigate to the camera view
export const checkCameraPermissionsAndOpenIt = async (
  navigation: any,
  getImagePathOrBarcodeResult: (path: string) => void,
  options: {
    title?: string;
    isBarCodeScanner?: boolean;
    MessageIconComponent?: React.ComponentType;
    infoMessage?: string;
  } = {},
) => {
  const hasPermissions = await CameraPermissionService.checkPermission();

  if (!hasPermissions) {
    const requestStatus =
      await CameraPermissionService.forceRequestPermission();

    if (!requestStatus) {
      return;
    }
  }

  await navigateToCameraViewScreen(
    navigation,
    getImagePathOrBarcodeResult,
    options,
  );
};

// Function to navigate to the camera view screen
const navigateToCameraViewScreen = async (
  navigation: any,
  getImagePathOrBarcodeResult: (path: string) => void,
  options: {
    title?: string;
    isBarCodeScanner?: boolean;
    MessageIconComponent?: React.ComponentType;
    infoMessage?: string;
  },
) => {
  navigation.navigate('CameraViewScreen', {
    ...options,
    getImagePathOrBarcodeResult,
  });
};
