import React from 'react';
import { ScrollView, View, StyleSheet, ViewStyle } from 'react-native';
import { Step, StepType } from '~/types/step.types';
import StepComponent from '~/components/protocol/StepComponent';
import StepActionRenderer from '~/components/protocol/StepActionRenderer';
import { gap16, padding20, padding12 } from '~/styles/spacing';
import colors from '~/styles/colors';
import ScreenWrapper from '~/screens/ScreenWrapper';
import ProtocolHeader from '~/components/protocol/ProtocolHeader';

interface StepComponentsProps {
  readonly step: Step;
}

export default function StepComponents({ step }: StepComponentsProps) {
  const isModal = step.type === StepType.MODAL;

  const actionsContainerStyle = [
    styles.actionsContainer,
    isModal ? styles.actionsRow : styles.actionsColumn,
  ];

  const Components = () => {
    const scrollable = step.config?.scrollable ?? true;
    if (scrollable) {
      return (
        <ScrollView
          key={step.stepId}
          bounces={false}
          contentContainerStyle={styles.listContainer}>
          <>
            <ProtocolHeader title={step.title} subtitle={step.subtitle ?? ''} />
            <View style={styles.componentContainer}>
              {step.components.map(component => (
                <StepComponent key={component.key} component={component} />
              ))}
            </View>
          </>
        </ScrollView>
      );
    }
    return (
      <>
        <ProtocolHeader title={step.title} subtitle={step.subtitle ?? ''} />
        <View style={styles.nonScrollableContainer}>
          {step.components.map(component => (
            <StepComponent key={component.key} component={component} />
          ))}
        </View>
      </>
    );
  };

  const Actions = () => {
    return (
      <View style={actionsContainerStyle}>
        {step.actions.map(action => (
          <StepActionRenderer
            key={`${action.label}-${action.key ?? ''}`}
            action={action}
            isRowAction={isModal}
            components={step.components}
            parentComponentType={step.type}
          />
        ))}
      </View>
    );
  };

  if (isModal) {
    return (
      <View style={styles.modalContainer}>
        <Components />
        <Actions />
      </View>
    );
  }

  return (
    <ScreenWrapper isKeyboardSensitive>
      <ScreenWrapper.Body withoutPadding>
        <Components />
      </ScreenWrapper.Body>
      <ScreenWrapper.Bottom>
        <Actions />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    overflow: 'hidden',
  } as ViewStyle,
  listContainer: {
    ...padding20,
    ...gap16,
  } as ViewStyle,
  actionsContainer: {
    ...gap16,
  } as ViewStyle,
  actionsRow: {
    ...padding12,
    borderTopWidth: 1,
    flexDirection: 'row',
    borderTopColor: colors.lightGrayishBlue,
  } as ViewStyle,
  actionsColumn: {
    flexDirection: 'column',
  } as ViewStyle,
  componentContainer: {
    gap: 5,
  },
  nonScrollableContainer: {
    gap: 5,
    flex: 1,
  },
  scrollStyle: {
    flex: 1,
  },
});
