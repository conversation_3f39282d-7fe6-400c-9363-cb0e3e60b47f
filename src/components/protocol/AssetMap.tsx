import React from 'react';
import { ImageSourcePropType, Text } from 'react-native';
import {
  GeofenceWarningWithBackground,
  HotWeather,
  ColdWeather,
  RedThermometer,
  StopWatchWithBackground,
  Warning,
} from '~/assets/icons';
import {
  Briefcase,
  Camera,
  MeshBackground,
  StopWatch,
} from '~/components/icons';
import colors from '~/styles/colors';

const iconMap: Record<string, React.ReactNode> = {
  briefcase: <Briefcase />,
  iconClock: <StopWatch color={colors.darkBlue500} />,
  warningWithBackground: <Warning />,
  camera: <Camera size={24} color={colors.white} />,
  stopwatchWithBackground: <StopWatchWithBackground />,
  mapMarkerCheckWithBackground: <GeofenceWarningWithBackground />,
  redThermometer: <RedThermometer />,
  hotWeather: <HotWeather />,
  coldWeather: <ColdWeather />,
  gridView: <MeshBackground>{<HotWeather />}</MeshBackground>,
};

const IconMap = ({
  name,
  size = 80,
}: {
  name: string | undefined;
  size?: number;
}) => {
  if (name === undefined || !iconMap[name]) {
    return <Text>Icon not found</Text>;
  }

  if (name === 'hotWeather' && size) {
    return <HotWeather width={size} height={size} />;
  }

  return iconMap[name];
};

const imageAssetMap: Record<string, ImageSourcePropType> = {
  'assets/images/survey_cooler_area.webp': require('~/assets/images/survey_cooler_area.webp'),
  'assets/images/survey_empty_cooler.webp': require('~/assets/images/survey_empty_cooler.webp'),
  'assets/images/survey_cooler_area_1.webp': require('~/assets/images/survey_cooler_area_1.webp'),
  'assets/images/survey_area_specimens_handled.webp': require('~/assets/images/survey_area_specimens_handled.webp'),
};

const ImageAssetMap = ({ name }: { name: string | undefined }) => {
  if (name === undefined || !imageAssetMap[name]) {
    return imageAssetMap['survey_cooler_area.webp'];
  }

  return imageAssetMap[name];
};

export { IconMap, ImageAssetMap };
