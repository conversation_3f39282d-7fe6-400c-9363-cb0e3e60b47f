import React from 'react';
import { View, Text, StyleSheet, TextStyle } from 'react-native';
import { grayishText, h5 } from '~/styles/text';

interface ProtocolHeaderProps {
  title?: string;
  subtitle?: string;
}

const ProtocolHeader = ({ title, subtitle }: ProtocolHeaderProps) => {
  if (!title && !subtitle) return null;

  return (
    <View style={styles.container}>
      {title && <Text style={styles.title}>{title}</Text>}
      {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
    </View>
  );
};

export default ProtocolHeader;

const styles = StyleSheet.create({
  container: {
    gap: 6,
  },
  title: {
    ...h5,
  } as TextStyle,
  subtitle: {
    ...grayishText,
  } as TextStyle,
});
