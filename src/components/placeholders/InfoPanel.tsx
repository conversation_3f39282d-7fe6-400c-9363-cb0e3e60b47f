import React from 'react';
import { View, Text, ViewStyle, TextStyle } from 'react-native';
import MeshBackground from '~/components/icons/MeshBackground';
import colors from '~/styles/colors';
import { paddingBottom6 } from '~/styles/spacing';
import { subHeaderTitle, grayishText } from '~/styles/text';
import { container, center } from '~/styles/views';

interface InfoPanelProps {
  icon: React.ReactNode;
  title: string;
  infoText: string;
}

const InfoPanel: React.FC<InfoPanelProps> = ({ icon, title, infoText }) => {
  return (
    <View style={styles.container}>
      <MeshBackground style={styles.background}>{icon}</MeshBackground>
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.infoText}>{infoText}</Text>
    </View>
  );
};

const styles = {
  container: {
    ...container,
    ...center,
    bottom: '10%',
  } as ViewStyle,
  background: {
    backgroundColor: colors.transGreen50,
  } as ViewStyle,
  title: {
    ...subHeaderTitle,
    ...paddingBottom6,
  } as TextStyle,
  infoText: {
    ...grayishText,
    textAlign: 'center',
  } as TextStyle,
};

export default InfoPanel;
