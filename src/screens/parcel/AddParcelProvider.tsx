import React, {
  createContext,
  useContext,
  useCallback,
  useMemo,
  useReducer,
  useState,
} from 'react';
import { Stop, StopType } from '~/types/stops.types';
import { Parcel, ParcelType } from '~/types/parcel.types';
import { ParcelManager } from '~/services/sync/parcelManager';
import {
  SortingOptionType,
  SORTING_OPTIONS,
} from '~/components/modals/SortModal';

export type AddParcelData = {
  quantity: number;
  type?: ParcelType;
  dropoffDestination?: string;
  isSignatureRequired?: boolean;
  comments?: string;
  posPhoto?: string;
  barcodeScannerResult?: string;
};

type ParcelAction =
  | { type: 'SET_TYPE'; payload: ParcelType }
  | { type: 'SET_QUANTITY'; payload: number }
  | { type: 'SET_DROPOFF'; payload: string }
  | { type: 'SET_SIGNATURE'; payload: boolean }
  | { type: 'SET_COMMENTS'; payload: string }
  | { type: 'SET_PHOTO'; payload: string }
  | { type: 'SET_BARCODE'; payload: string };

const parcelReducer = (
  state: AddParcelData,
  action: ParcelAction,
): AddParcelData => {
  switch (action.type) {
    case 'SET_TYPE':
      return { ...state, type: action.payload };
    case 'SET_QUANTITY':
      return { ...state, quantity: action.payload };
    case 'SET_DROPOFF':
      return { ...state, dropoffDestination: action.payload };
    case 'SET_SIGNATURE':
      return { ...state, isSignatureRequired: action.payload };
    case 'SET_COMMENTS':
      return { ...state, comments: action.payload };
    case 'SET_PHOTO':
      return { ...state, posPhoto: action.payload };
    case 'SET_BARCODE':
      return { ...state, barcodeScannerResult: action.payload };
    default:
      return state;
  }
};

type AddParcelContextType = {
  // Data
  stop: Stop;
  stopList: Stop[];
  stopType: StopType;
  parcelData: AddParcelData;
  parcelTypes: ParcelType[];
  selectedSortingOption: SortingOptionType;

  // Methods
  setSelectedSortingOption: (option: SortingOptionType) => void;
  setParcelType: (type: ParcelType) => void;
  setParcelQuantity: (quantity: number) => void;
  setDropoffDestination: (destination: string) => void;
  setIsSignatureRequired: (required: boolean) => void;
  setComments: (comments: string) => void;
  setPosPhoto: (photo: string) => void;
  setBarcodeResult: (barcode: string) => void;
  saveParcel: () => Promise<boolean>;

  // Computed Properties
  isContinueButtonEnabled: (screen: 'first' | 'second' | 'third') => boolean;
};

export interface AddParcelProviderProps {
  stop: Stop;
  stopList: Stop[];
  stopType: StopType;
  parcelTypes: ParcelType[];
  children: React.ReactNode;
}

const AddParcelContext = createContext<AddParcelContextType | undefined>(
  undefined,
);

export const AddParcelProvider: React.FC<AddParcelProviderProps> = ({
  stop,
  stopList,
  stopType,
  parcelTypes,
  children,
}) => {
  const [parcelData, dispatch] = useReducer(parcelReducer, { quantity: 1 });

  const [selectedSortingOption, setSelectedSortingOption] =
    useState<SortingOptionType>(SORTING_OPTIONS.NEXT_AVAILABLE_STOPS);

  const setParcelType = useCallback((type: ParcelType) => {
    dispatch({ type: 'SET_TYPE', payload: type });
  }, []);

  const setParcelQuantity = useCallback((quantity: number) => {
    dispatch({ type: 'SET_QUANTITY', payload: quantity });
  }, []);

  const setDropoffDestination = useCallback((destination: string) => {
    dispatch({ type: 'SET_DROPOFF', payload: destination });
  }, []);

  const setIsSignatureRequired = useCallback((required: boolean) => {
    dispatch({ type: 'SET_SIGNATURE', payload: required });
  }, []);

  const setComments = useCallback((comments: string) => {
    dispatch({ type: 'SET_COMMENTS', payload: comments });
  }, []);

  const setPosPhoto = useCallback((photo: string) => {
    dispatch({ type: 'SET_PHOTO', payload: photo });
  }, []);

  const setBarcodeResult = useCallback((barcode: string) => {
    dispatch({ type: 'SET_BARCODE', payload: barcode });
  }, []);

  const saveParcel = useCallback(async (): Promise<boolean> => {
    try {
      const parcelObject: Partial<Parcel> = {
        Pickup__c: stop.Id,
        Delivery__c: parcelData.dropoffDestination,
        Requires_Signature__c: parcelData.isSignatureRequired ?? null,
        Reference_Required__c: false,
        Parcel_Type_Name__c: parcelData.type?.Name ?? null,
        Parcel_Type_Definition__c: parcelData.type?.Id ?? null,
        Quantity__c: parcelData.quantity ?? null,
      };

      await ParcelManager.createParcel(parcelObject);
      return true;
    } catch (error) {
      console.error('AddParcelProvider: saveParcel():', error);
      return false;
    }
  }, [parcelData, stop?.Id]);

  const isContinueButtonEnabled = useCallback(
    (screen: 'first' | 'second' | 'third'): boolean => {
      switch (screen) {
        case 'first':
          return Boolean(parcelData.type && parcelData.quantity);
        case 'second':
          return Boolean(
            parcelData.dropoffDestination &&
              parcelData.isSignatureRequired !== undefined,
          );
        case 'third':
          return Boolean(
            parcelData.posPhoto || parcelData.barcodeScannerResult,
          );
        default:
          return false;
      }
    },
    [parcelData],
  );

  const values = useMemo(
    () => ({
      stop,
      stopList,
      stopType,
      parcelData,
      parcelTypes,
      selectedSortingOption,
      setSelectedSortingOption,
      setParcelType,
      setParcelQuantity,
      setDropoffDestination,
      setIsSignatureRequired,
      setComments,
      setPosPhoto,
      setBarcodeResult,
      saveParcel,
      isContinueButtonEnabled,
    }),
    [
      stop,
      stopList,
      stopType,
      parcelData,
      parcelTypes,
      selectedSortingOption,
      setParcelType,
      setParcelQuantity,
      setDropoffDestination,
      setIsSignatureRequired,
      setComments,
      setPosPhoto,
      setBarcodeResult,
      saveParcel,
      isContinueButtonEnabled,
    ],
  );

  return (
    <AddParcelContext.Provider value={values}>
      {children}
    </AddParcelContext.Provider>
  );
};

export const useAddParcelContext = () => {
  const context = useContext(AddParcelContext);

  if (!context) {
    throw new Error(
      'useAddParcelContext must be used within an AddParcelProvider',
    );
  }

  return context;
};
