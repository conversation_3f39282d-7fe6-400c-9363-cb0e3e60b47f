import React, { useState, useRef, useMemo } from 'react';
import { StyleSheet, Text, View, ViewStyle } from 'react-native';
import SignatureCapture from '~/components/SignatureCapture';
import FilledButton from '~/components/buttons/FilledButton';
import { container, deviceWidth } from '~/styles/views';
import { h5 } from '~/styles/text';
import TextInput from '~/components/inputs/TextInput';
import { primarySolidButton } from '~/styles/buttons';
import en from '~/localization/en';
import ScreenWrapper from '~/screens/ScreenWrapper';
import CardWrapper from '~/components/cards/CardWrapper';
import { useNavigation } from '@react-navigation/native';

/**
 * A Collect Signature Screen component that allows users to sign and add an optional comment.
 *
 * @component
 * @param {string} props.title - The title to display on the screen's header.
 * @param {function} props.onSignatureConfirm - A callback function that returns the signature as a string.
 * @param {function} props.onCommentSubmit - A callback function that returns the comment as a string.
 * @param {function} props.onInfoIconPress - A callback function triggered when the info icon on the header is pressed.
 *
 * @example
 * // Example usage of CollectSignature component
 * <CollectSignature
 *   title="Collect Signature"
 *   onSignatureConfirm={(signature) => console.log(signature)}
 *   onCommentSubmit={(comment) => console.log(comment)}
 *   onInfoIconPress={() => console.log('Info icon pressed')}
 * />
 */

type CollectSignatureProps = {
  route: {
    params: {
      title: string;
      signatureURI: string;
      comments: string;
      onValueChange: (signature: string, comments: string) => void;
      onCompleteDropOff: (signature: string, comments: string) => void;
    };
  };
};

const CollectSignature: React.FC<CollectSignatureProps> = ({ route }) => {
  const navigation = useNavigation();
  const commentValue = useRef<string>(route.params?.comments || '');
  const [signatureURI, setSignatureURI] = useState<string>(
    route.params?.signatureURI ?? '',
  );

  // Memoize signature capture to prevent unnecessary re-renders
  const signatureCapture = useMemo(() => {
    return (
      <SignatureCapture
        onConfirmCallback={(sign: string) => {
          setSignatureURI(sign);
          route?.params.onValueChange(sign, commentValue.current);
        }}
        signatureURI={signatureURI}
      />
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCompleteDropOff = async () => {
    navigation.goBack();
    route.params?.onCompleteDropOff(signatureURI, commentValue.current);
  };

  return (
    <ScreenWrapper isKeyboardSensitive>
      <ScreenWrapper.Body>
        <View style={styles.container}>
          <Text style={styles.signBelow}>{en.sign_below}</Text>

          {signatureCapture}

          <CardWrapper>
            <TextInput
              id={'CollectSignature.InputField.comment'}
              defaultValue={commentValue.current}
              onChangeText={(value: string) => {
                commentValue.current = value;
                route?.params.onValueChange(signatureURI, value);
              }}
              label={en.add_comment}
              placeholder={en.additional_information}
            />
          </CardWrapper>
        </View>
      </ScreenWrapper.Body>
      <ScreenWrapper.Bottom>
        <FilledButton
          id={'CollectSignature.FilledButton.complete'}
          title={en.complete_dropoff}
          onClick={handleCompleteDropOff}
          isDisabled={!signatureURI}
          color="primary"
          style={[primarySolidButton, { width: deviceWidth * 0.9 }]}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    ...container,
    gap: 8,
  },
  signBelow: {
    ...h5,
  } as ViewStyle,
});

export default CollectSignature;
