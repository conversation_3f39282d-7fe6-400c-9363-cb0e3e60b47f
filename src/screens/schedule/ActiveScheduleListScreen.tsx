import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextStyle,
  RefreshControl,
  ViewStyle,
  SectionList,
  SectionListData,
} from 'react-native';
import colors from '~/styles/colors';
import { h3 } from '~/styles/text';
import { RouteSummary } from '~/types/routes.types';
import en from '~/localization/en';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { getStepperPillColors } from '~/utils/route';
import { center, container } from '~/styles/views';
import RouteCard from '~/components/cards/RouteCard';
import {
  paddingBottom16,
  paddingHorizontal16,
  paddingTop32,
} from '~/styles/spacing';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StopWithRelations } from '~/types/stops.types';
import StopCard from '~/components/cards/StopCard';
import InfoPanel from '~/components/placeholders/InfoPanel';
import { RouteWithBackground } from '~/assets/icons';
import { getStopStepperPillColors } from '~/utils/stops';
import { CheckInCard } from '~/components/cards/CheckInCard';
import { getFormattedTimeUsingOffset } from '~/utils/dateAndTime';
import { IconMap } from '~/components/protocol/AssetMap';

const ActiveScheduleList = ({
  date,
  tasks,
  nextTask,
  onRefresh,
  headerComponent,
}: {
  tasks: any[];
  nextTask?: any;
  onRefresh: () => void;
  date: string;
  headerComponent?: React.ReactNode;
}) => {
  const navigation = useNavigation();
  const isFocused = useIsFocused();
  const bottomBarHeight = useBottomTabBarHeight();
  const { bottom } = useSafeAreaInsets();

  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      onRefresh();
    } finally {
      setRefreshing(false);
    }
  };

  const renderSectionHeader = ({
    section,
  }: {
    section: SectionListData<StopWithRelations>;
  }) => {
    return <Text style={styles.sectionHeaderText}>{section.title}</Text>;
  };

  const renderEmptyList = () => {
    if (nextTask) return null;

    return (
      <View style={[container, center as ViewStyle, paddingTop32]}>
        <InfoPanel
          icon={<RouteWithBackground />}
          title={en.no_tasks_assigned}
          infoText={en.no_tasks_assigned_details}
        />
      </View>
    );
  };

  const handleStopSelection = (
    stop: StopWithRelations,
    isCurrent: boolean = false,
  ) => {
    navigation.navigate('StopDetailScreen', {
      date,
      stopId: stop.Id,
      isCurrent,
      title: stop.Name,
    });
  };

  const handleRouteSelection = async (route: RouteSummary) => {
    const formattedTime = await getFormattedTimeUsingOffset(
      route.Planned_Start__c,
      route.UTC_Offset__c ?? 0,
    );

    navigation.navigate('StopDetailScreen', {
      showPlaceholder: true,
      placeholderTime: formattedTime,
      title: route.Name,
    });
  };

  const launchProtocolFlow = async (protocolData: any) => {
    navigation.navigate('ProtocolEngineStack', protocolData);
  };

  const renderTaskCard = ({ item }: { item: any }) => {
    if (item.attributes?.includes('Stop__c')) {
      const isCurrent = item && nextTask && item?.Id === nextTask?.Id;

      return (
        <StopCard
          stop={item}
          stepperChipColor={getStopStepperPillColors(item.Status__c)}
          current={isCurrent}
          onItemPress={() => handleStopSelection(item, isCurrent)}
        />
      );
    } else if (item.attributes?.includes('Route_Summary__c')) {
      return (
        <RouteCard
          route={item}
          stepperChipColor={getStepperPillColors(item.Status__c)}
          onItemPress={() => handleRouteSelection(item)}
        />
      );
    }
    const description =
      item.protocol.description.type === 'dynamic'
        ? item.scenario[item.protocol.description.value]
        : item.protocol.description;
    const icon = item.protocol?.icon ? (
      <IconMap name={item.protocol?.icon} size={40} />
    ) : null;
    return (
      <CheckInCard
        title={item.protocol.label}
        description={description}
        isTimerActive={false}
        isButtonEnabled={true}
        onPress={() => launchProtocolFlow(item)}
        icon={icon}
      />
    );
  };

  const renderItemSeparator = () => {
    return <View style={styles.itemSeparator} />;
  };

  const routeContainerStyle = {
    ...styles.routeContainer,
    paddingBottom:
      bottomBarHeight +
      (bottom ?? styles.routeContainer.paddingBottom) +
      styles.routeContainer.paddingBottom,
  };

  const sections = useMemo(() => {
    return [
      ...(nextTask ? [{ title: en.current, data: [nextTask] }] : []),
      ...(tasks?.length ? [{ title: en.upcoming, data: tasks }] : []),
    ];
  }, [nextTask, tasks]);

  const renderRefreshControl = () => {
    if (isFocused) {
      return (
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      );
    }
  };

  return (
    <ScreenWrapper>
      <SectionList
        stickySectionHeadersEnabled={false}
        sections={sections}
        contentContainerStyle={routeContainerStyle}
        keyExtractor={item => item?.Id}
        nestedScrollEnabled
        renderItem={renderTaskCard}
        renderSectionHeader={renderSectionHeader}
        ListEmptyComponent={renderEmptyList}
        ItemSeparatorComponent={renderItemSeparator}
        refreshControl={renderRefreshControl()}
        ListHeaderComponent={<>{headerComponent}</>}
      />
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  routeContainer: {
    ...paddingHorizontal16,
    ...paddingBottom16,
  },
  sectionHeaderText: {
    ...h3,
    fontSize: 20,
    color: colors.textBlack,
    marginTop: 24,
    marginBottom: 16,
  } as TextStyle,
  itemSeparator: {
    height: 16,
  } as ViewStyle,
});

export default ActiveScheduleList;
