import React, { useMemo, useState } from 'react';
import Title from '~/components/text/Title';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StopWithRelations, TaskListItem } from '~/types/stops.types';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '~/navigation/RouteStack';
import { padding16 } from '~/styles/spacing';
import FilledButton from '~/components/buttons/FilledButton';
import { primarySolidButton, buttonFullWidth } from '~/styles/buttons';
import RadioServiceItemCard from '~/components/select/radio/RadioServiceItemCard';
import CardWrapper from '~/components/cards/CardWrapper';
import en from '~/localization/en';
import { useTaskStore } from '~/store/useCompletedTasksStore';

interface SelectServiceScreenProps {
  route: {
    params: {
      stop: StopWithRelations;
      stopList: StopWithRelations[];
      taskList: TaskListItem[];
      isDelivery: boolean;
      isPickup: boolean;
      isService: boolean;
    };
  };
}

const SelectServiceScreen = ({ route }: SelectServiceScreenProps) => {
  const { completedTasks } = useTaskStore();
  const { stop, stopList, taskList } = route.params;
  const { bottom } = useSafeAreaInsets();
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const [selectedTask, setSelectedTask] = useState<TaskListItem | null>(null);

  const scrollContainerStyle = useMemo(
    () => ({
      ...padding16,
      paddingBottom: (bottom ?? padding16.padding) + padding16.padding,
    }),
    [bottom],
  );

  const navigateToStopOverview = () => {
    navigation.navigate('StopOverviewScreen', {
      title: stop.Name,
      task: selectedTask,
      stop,
      stopList,
      numberOfServices: taskList.length,
      taskList: taskList,
    });
  };

  const getButtonTitle = () => {
    if (selectedTask !== null) {
      return en.select_service_continue_to.replace(
        '{{task}}',
        selectedTask.type.toLowerCase(),
      );
    }

    return en.select_service_continue;
  };

  return (
    <ScreenWrapper>
      <ScreenWrapper.Body withoutPadding>
        <ScrollView style={scrollContainerStyle}>
          <Title
            title={en.select_service_title}
            subtitle={en.select_service_subtitle}
            icon={null}
          />

          {taskList.map(item => (
            <CardWrapper key={item.id} withShadow={true}>
              <RadioServiceItemCard
                key={item.id}
                item={item}
                isSelected={item.id === selectedTask?.id}
                onItemSelected={selectedItem => {
                  setSelectedTask(selectedItem);
                }}
                isCompleted={completedTasks.includes(item.id)}
                allowSelectionWhenCompleted={false}
              />
            </CardWrapper>
          ))}
        </ScrollView>
      </ScreenWrapper.Body>
      <ScreenWrapper.Bottom>
        <FilledButton
          id={'SelectServiceScreen.FilledButton.complete'}
          title={getButtonTitle()}
          isDisabled={selectedTask === null}
          onClick={navigateToStopOverview}
          style={[buttonFullWidth, primarySolidButton]}
          color={'primary'}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

export default SelectServiceScreen;
