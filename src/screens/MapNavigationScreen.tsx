import React, { useEffect, useState, useCallback } from 'react';
import {
  StyleSheet,
  ActivityIndicator,
  View,
  Platform,
  Text,
} from 'react-native';
import type { FC } from 'react';
import { useRoute, RouteProp, useFocusEffect } from '@react-navigation/native';
import { Location } from 'react-native-background-geolocation';

import ScreenWrapper from '~/screens/ScreenWrapper';
import type { ScheduleStackParamList } from '~/navigation/ScheduleStack';
import { getCurrentLocationFromGeolocation } from '~/services/location/LocationService';
import StopActionButton from '~/components/buttons/StopActionButton';
import MapboxNavigationIOS from '~/components/Mapbox/ios/MapboxNavigation';
import MapboxNavigationAndroid from '~/components/Mapbox/android/MapboxNavigation';
import en from '~/localization/en';

const MapNavigationScreen: FC = () => {
  const route =
    useRoute<RouteProp<ScheduleStackParamList, 'MapNavigationScreen'>>();
  const {
    stop,
    currentRoute,
    isDelivery,
    isPickup,
    isService,
    stopList,
    lastCompletedStop,
  } = route.params;

  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [mapKey, setMapKey] = useState(Date.now());

  const [error, setError] = useState<string | undefined>();

  const fetchDriverCoordinates = useCallback(async () => {
    const location = await getCurrentLocationFromGeolocation();

    if (!location?.coords) {
      setError(en.unableToFetchLocation);
      return;
    }
    setCurrentLocation(location);
  }, []);

  useFocusEffect(
    useCallback(() => {
      // Regenerate the key every time the screen comes into focus
      setMapKey(Date.now());
    }, []),
  );

  useEffect(() => {
    fetchDriverCoordinates();
  }, [fetchDriverCoordinates]);

  const latitude = currentLocation?.coords.latitude;
  const longitude = currentLocation?.coords.longitude;
  const isValidCoordinates =
    typeof latitude === 'number' && typeof longitude === 'number';

  const renderMapNavigation = () => {
    if (Platform.OS === 'ios') {
      return (
        <MapboxNavigationIOS
          origin={[longitude!, latitude!]}
          destination={[
            stop?.Stop_Coordinates__Longitude__s ?? 0,
            stop?.Stop_Coordinates__Latitude__s ?? 0,
          ]}
          onCancelNavigation={() =>
            console.info('MapboxNavigationIOS: Navigation cancelled')
          }
          onError={e => setError(e.nativeEvent?.message)}
          shouldSimulateRoute={false}
          hideStatusView
        />
      );
    }

    return (
      <MapboxNavigationAndroid
        key={mapKey}
        startOrigin={{
          latitude: latitude!,
          longitude: longitude!,
        }}
        destination={{
          latitude: stop?.Stop_Coordinates__Latitude__s ?? 0,
          longitude: stop?.Stop_Coordinates__Longitude__s ?? 0,
          title: 'Stop',
        }}
        travelMode="driving-traffic"
        style={styles.container}
        distanceUnit="metric"
        onCancelNavigation={() =>
          console.info('MapboxNavigationAndroid: Navigation cancelled')
        }
        onError={e => setError(e.message)}
      />
    );
  };

  if (!isValidCoordinates || error) {
    return (
      <ScreenWrapper>
        <ScreenWrapper.Body>
          <View style={styles.loaderContainer}>
            {error ? (
              <Text>{error}</Text>
            ) : (
              <>
                <ActivityIndicator size="large" />
                <Text>{'Loading map...'}</Text>
              </>
            )}
          </View>
        </ScreenWrapper.Body>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      <ScreenWrapper.Body withoutPadding>
        {renderMapNavigation()}
      </ScreenWrapper.Body>

      <ScreenWrapper.Bottom>
        <StopActionButton
          lastCompletedStop={lastCompletedStop}
          nextStop={stop}
          currentRoute={currentRoute}
          stopList={stopList}
          isPickup={isPickup}
          isDelivery={isDelivery}
          isService={isService}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

export default MapNavigationScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
