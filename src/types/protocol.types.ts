/* eslint-disable no-unused-vars */
import { Condition } from '~/types/condition.types';
import { ProtocolActionsMap } from '~/types/protocolActions.types.ts';
import { Step } from '~/types/step.types';

export enum ProtocolType {
  PRE_ROUTE_START = 'preRouteStart',
  PRE_ARRIVE_STOP = 'preArriveStop',
  PRE_COMPLETE_STOP = 'preCompleteStop',
  POST_ROUTE_COMPLETE = 'postRouteComplete',
  POST_ARRIVE_STOP = 'postArriveStop',
}

export type Protocol = {
  id: string;
  label: string;
  protocolType: string;
  version: string;
  executionCriteria: Condition;
  steps: Step[];
  actions: ProtocolActionsMap;
};

export type ProtocolVerificationRecord = {
  attributes?: string;
  Id: string;
  Protocol_Step_Id__c: string | null;
  Protocol__c: string | null;
  Field_Value__c: string | null;
  Daily_Schedule__c: string | null;
  isSyncReady?: boolean;
  isSynced?: boolean;
  retryCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
};
