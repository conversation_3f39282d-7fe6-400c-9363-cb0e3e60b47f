import { Parcel, ParcelResponse } from '~/types/parcel.types';
import { Service, ServiceResponse } from '~/types/service.types';
import { Attributes, BaseResponse } from '~/types/shared.types';

type Location = {
  attributes: Attributes;
  Arrival_Verification_Method__c: string | null;
  Name: string;
  Coordinates__Latitude__s: number;
  Coordinates__Longitude__s: number;
  Driver_Wait_Time_Duration__c: number | null;
  Geofencing_Distance__c: number | null;
  Business_Hours__c: string | null;
};

export const StopType = {
  Delivery: 'Delivery',
  Pickup: 'Pickup',
  Exchange: 'Exchange',
  Meet: 'Meet',
  Flight: 'Flight',
  Start: 'Start',
  End: 'End',
  Break: 'Break',
  Service: 'Service',
} as const;

export const StopStatusType = {
  SCHEDULED: 'SCHEDULED',
  ARRIVED: 'ARRIVED',
  COMPLETE: 'COMPLETE',
  INACTIVE: 'INACTIVE',
  LATE: 'LATE',
  OPEN: 'OPEN',
  ON_SCHEDULE: 'ON_SCHEDULE',
} as const;

export type StopStatusType =
  (typeof StopStatusType)[keyof typeof StopStatusType];

export type StopType = (typeof StopType)[keyof typeof StopType];

export type Stop = {
  attributes: Attributes;
  LastModifiedDate: string;
  Id: string;
  Name: string;
  Stop_Time_Min__c: string | null;
  Stop_Time_Preferred__c: string | null;
  Stop_Time_Max__c: string | null;
  ETA__c: string | null;
  Type__c: StopType | null;
  Address__c: string | null;
  Address_1__c: string;
  Address_2__c: string | null;
  City__c: string;
  Postal_Code__c: string;
  State__c: string;
  Status__c: StopStatusType;
  Post_Date__c: string | null;
  Stop_Coordinates__Latitude__s: number;
  Stop_Coordinates__Longitude__s: number;
  Notes__c: string | null;
  Notes_Other__c: string | null;
  POD__c: string | null;
  POD_Comments__c: string | null;
  Customer_Reference_1__c: string | null;
  Customer_Reference_2__c: string | null;
  Pieces__c: number;
  Summary__c: string;
  Arrival_Time__c: string | null;
  Completed_Time__c: string | null;
  Geofencing_Distance__c: number | null;
  Location__r: Location;
  Coordinates__c: string;
  Wait_Time_Minutes__c: number | null;
  SNO_Bypass_DateTime__c: string | null;
  SNO_Driver_Waiting__c: boolean;
  SNO_Tags__c: string | null;
  No_Perimeter_Check_Notes__c: string | null;
  Pickup_Contact__c: string | null;
  Pickup_Comments__c: string | null;
  Delivery_Contact__c: string | null;
  Delivery_Comments__c: string | null;
  Driver_Initiated_Early_Departure__c: boolean;
  Service_Comments__c: string;
  Proof_of_Service__c: string | null;
  Daily_Schedule__c: string | null;
};

export interface StopWithRelations extends Stop {
  Pickup_Parcels__r: ParcelResponse;
  Delivery_Parcels__r: ParcelResponse;
  Services__r: ServiceResponse;
}

export type StopResponse = BaseResponse & {
  records: StopWithRelations[] | null;
};

export type TaskListItem = {
  id: string;
  type: StopType;
  data: Parcel[] | Service;
  icon: React.ReactNode;
};
