/* eslint-disable no-unused-vars */
import { Condition } from '~/types/condition.types';
import { Component } from '~/types/component.types';
import { StepAction } from '~/types/protocolActions.types';

export enum StepType {
  PAGE = 'page',
  MODAL = 'modal',
  BACKGROUND = 'background',
}

type StepHeader = {
  title?: {
    type: 'static' | 'dynamic';
    value: string;
  };
  progress?: string;
};

export type Step = {
  stepId: string;
  type: StepType;
  header: StepHeader;
  title?: string;
  subtitle?: string;
  displayIf?: Condition;
  components: Component[];
  actions: StepAction[];
  config?: {
    scrollable?: boolean;
  };
};
