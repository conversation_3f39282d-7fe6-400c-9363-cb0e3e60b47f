export const NotificationType = {
  newRouteText: 'new_route',
  routeReadyText: 'route_ready',
  newStopAddedText: 'new_stop_added',
  stopNotesUpdatedText: 'stop_notes_updated',
  stopRescheduledText: 'stop_rescheduled',
  stopCancelledText: 'stop_cancelled',
  addMessageText: 'add_message',
  openChatText: 'open_chat',
  autoSyncUp: 'auto_syncup',
} as const;

export type NotificationType =
  (typeof NotificationType)[keyof typeof NotificationType];

export type AdditionalData = Record<string, any> | undefined;

export type NewMessageReceivedAdditionalData = {
  messageId: string;
  typeName: 'add_message';
  chatId: string;
  actingUserId: string;
  orgId: string;
};

export type NewChatOpenedAdditionalData = {
  typeName: 'open_chat';
  participantIds: Array<string>;
  chatId: string;
  actingUserId: string;
  orgId: string;
};

export const NotificationDisplayType = {
  Foreground: 'foreground',
  Background: 'background',
  Quit: 'quit',
} as const;
