/* eslint-disable no-unused-vars */
import { StyleProp, ViewStyle } from 'react-native';
import { Condition } from '~/types/condition.types';
import { StepAction } from '~/types/protocolActions.types';

export enum ComponentType {
  DISPLAY_TEXT = 'DisplayText',
  TEXT_INPUT = 'TextInput',
  RADIO_SELECT = 'RadioSelect',
  CHECKBOX_GROUP = 'CheckboxGroup',
  DROPDOWN = 'Dropdown',
  IMAGE = 'Image',
  BUTTON = 'Button',
  PHOTO_CAPTURE = 'PhotoCapture',
  TIMER = 'Timer',
  SLIDER = 'Slider',
  ICON = 'Icon',
  TEMPERATURE_INFO = 'ExpectedTemperature',
}

export enum ValidationType {
  REQUIRED = 'Required',
  MIN_LENGTH = 'MinLength',
  MAX_LENGTH = 'MaxLength',
  MIN_VALUE = 'MinValue',
  MAX_VALUE = 'MaxValue',
  CUSTOM = 'Custom',
  REGEX = 'Regex',
  EMAIL = 'Email',
  SSN = 'SSN',
  FILE_SIZE_MAX = 'FileSizeMax',
  FILE_TYPES_ALLOWED = 'FileTypesAllowed',
  PHONE_NUMBER = 'PhoneNumber',
}

export type Position = 'left' | 'right' | 'center';

export type IconPosition = Extract<Position, 'left' | 'right'>;

export type TextPosition = Extract<Position, 'left' | 'center' | 'right'>;

export type ButtonVariant = 'filled' | 'outlined' | 'link';

export type ButtonColor = 'primary' | 'secondary';

export type TextOptions = {
  content: string;
  type: 'title' | 'subtitle';
  align?: TextPosition;
  wrapInCard?: boolean;
};

export type TextInputOptions = {
  label?: string;
  defaultValue?: string;
  placeholder?: string;
};

export type ChoiceOptions = {
  label?: string;
  wrapInCard?: boolean;
  defaultValue?: string;
  choices: { label: string; value: string }[];
  style?: {
    backgroundColor?: string;
  };
};

export type PhotoCaptureOptions = {
  label?: string;
  cameraTitle?: string;
  photoTitle?: string;
  multiple?: boolean;
  readonly?: boolean;
  linkedEntities?: string[];
  uris: string[] | string;
};

export type ButtonOptions = {
  label?: string;
  variant?: ButtonVariant;
  icon?: string;
  iconPosition?: IconPosition;
  priority?: ButtonColor;
  action?: Pick<StepAction, 'type' | 'targetStepId'>;
};

export type ImageOptions = Omit<PhotoCaptureOptions, 'linkedEntities'>;

export type IconOptions = {
  name: string;
  iconPosition?: Position;
  style?: StyleProp<ViewStyle>;
};

export type TimerOptions = {
  duration?: number;
  id?: string;
};

export type SliderOptions = {
  label?: string;
  defaultValue?: number;
  min: number;
  max: number;
  step: number;
  suffix?: string;
};

export type Validation = {
  type: ValidationType;
  value?: string | number;
  validationErrorMsg?: string;
};

export type TemperatureInfoOptions = {
  location: string;
  temperature: number;
  unit?: '°C' | '°F';
  icon?: string;
  style?: StyleProp<ViewStyle>;
};

export type Component = {
  key: string;
  displayIf?: Condition;
  validations?: Validation[];
  linkedEntities?: string[];
} & (
  | { type: ComponentType.DISPLAY_TEXT; options: TextOptions }
  | { type: ComponentType.TEXT_INPUT; options: TextInputOptions }
  | { type: ComponentType.RADIO_SELECT; options: ChoiceOptions }
  | { type: ComponentType.CHECKBOX_GROUP; options: ChoiceOptions }
  | { type: ComponentType.DROPDOWN; options: ChoiceOptions }
  | { type: ComponentType.PHOTO_CAPTURE; options: PhotoCaptureOptions }
  | { type: ComponentType.BUTTON; options: ButtonOptions }
  | { type: ComponentType.IMAGE; options: ImageOptions }
  | { type: ComponentType.TIMER; options: TimerOptions }
  | { type: ComponentType.SLIDER; options: SliderOptions }
  | { type: ComponentType.ICON; options: IconOptions }
  | { type: ComponentType.TEMPERATURE_INFO; options: TemperatureInfoOptions }
);
