import BackgroundGeolocation, {
  Location,
} from 'react-native-background-geolocation';
import BackgroundFetch from 'react-native-background-fetch';
import Geolocation from 'react-native-geolocation-service';
import DeviceInfo from 'react-native-device-info';
import { postLocationToServer } from '~/api/location';
import { Alert, AppState, Platform } from 'react-native';
import { getDataFromRealm } from '~/db/realm/operations/data.operations';

let routeId: string | undefined;
let stopId: string | undefined;
let dailyScheduleId: string | undefined;
let lockCheckInOrder: boolean = true;
let isHeartbeatEnabled = false;
let lastPostedLocationKey = '';
let lastPostedLocationTimestamp = 0;
const HEARTBEAT_INTERVAL = Platform.OS === 'ios' ? 120 : 90;
const MIN_TIME_BETWEEN_UPDATES = 60 * 1000;
let hasAppStateListener = false;

/**
 * Initialize BackgroundGeolocation and BackgroundFetch
 */
export const initLocationService = () => {
  console.info('📍 LocationService initialized');
  initBackgroundGeolocation();
  initBackgroundFetch();
};

/**
 * Set Date,RouteId and StopId
 * @param {string} newRouteId
 * @param {string} [newStopId] - Optional Stop ID
 * @param {string} [newDailyScheduleId] - Optional Daily Schedule ID

 */
export const setRouteAndStopId = (
  newRouteId?: string,
  newStopId?: string,
  newDailyScheduleId?: string,
  newLockCheckInOrder?: boolean,
) => {
  routeId = newRouteId;
  stopId = newStopId;
  dailyScheduleId = newDailyScheduleId;
  lockCheckInOrder = newLockCheckInOrder ?? true;
};

/**
 * Start heartbeat
 */
export const startHeartbeat = () => {
  isHeartbeatEnabled = true;
  console.info('[Heartbeat] Enabled');
};

/**
 * Stop heartbeat
 */
export const stopHeartbeat = () => {
  isHeartbeatEnabled = false;
  console.info('[Heartbeat] Disabled');
};

/**
 * Sets routeId and stopId from Realm if not already set.
 */
export const fetchAndSetRouteAndStopIds = async () => {
  if (!routeId || !stopId) {
    try {
      const [
        fetchedRouteId,
        fetchedStopId,
        fetchedDailyScheduleId,
        fetchedLockCheckInOrder,
      ] = await Promise.all([
        getDataFromRealm<string | null>('routeId'),
        getDataFromRealm<string | null>('stopId'),
        getDataFromRealm<string | null>('dailyScheduleId'),
        getDataFromRealm<boolean>('lockCheckInOrder'),
      ]);
      routeId = fetchedRouteId ?? undefined;
      stopId = fetchedStopId ?? undefined;
      dailyScheduleId = fetchedDailyScheduleId ?? undefined;
      lockCheckInOrder = fetchedLockCheckInOrder ?? true;
    } catch (error) {
      console.error('LocationService: fetchAndSetRouteAndStopIds():', error);
    }
  }
};

/**
 * Post location to the backend
 * @param {Location} location
 */
export const postLocation = async (
  location: Location,
  isCheckIn: boolean = false,
  extras?: { event?: string },
) => {
  await fetchAndSetRouteAndStopIds();

  if (!stopId && lockCheckInOrder) {
    console.info(
      'Could not send location to backend as no eligible stop is there',
    );
    return;
  }

  const source = extras?.event || (isCheckIn ? 'checkin' : 'unknown');
  const { timestamp, uuid } = location;
  const lat = location.coords?.latitude;
  const lng = location.coords?.longitude;

  if (lat === undefined || lng === undefined) {
    console.warn('Skipping post location: invalid lat/lng in location');
    return;
  }

  const locationKey = uuid;

  // Block duplicate lat/lng/timestamp from ANY source
  if (!isCheckIn && locationKey === lastPostedLocationKey) {
    return;
  }

  // Enforce minimum interval for motion and heartbeat
  const currentTime = new Date(timestamp).getTime();
  const lastTime = lastPostedLocationTimestamp
    ? new Date(lastPostedLocationTimestamp).getTime()
    : 0;

  const differenceInMs = Math.abs(currentTime - lastTime);
  const throttleInMs =
    source === 'heartbeat'
      ? HEARTBEAT_INTERVAL * 1000
      : MIN_TIME_BETWEEN_UPDATES;

  if (!isCheckIn && differenceInMs < throttleInMs) {
    console.info(
      `Ignoring this location packet as location data is too frequent. Time since last update: ${differenceInMs}ms`,
    );
    return;
  }

  lastPostedLocationKey = locationKey;
  lastPostedLocationTimestamp = currentTime;

  console.info('Going to process location');
  postLocationToServer(
    location,
    routeId,
    stopId,
    dailyScheduleId,
    lockCheckInOrder,
    isCheckIn,
  ); // Send the request to the backend
};

/**
 * Get Current Location from react-native-background-geolocation library
 * @returns {Promise<Location | null>} Location object or null
 */
export const getCurrentLocationFromGeolocation = async (
  enhancedAccuracy: boolean = false,
) => {
  try {
    const location = await BackgroundGeolocation.getCurrentPosition({
      maximumAge: 30000,
      desiredAccuracy: 20,
      samples: enhancedAccuracy ? 2 : 1,
    });
    console.info('📍 Current Location:', location);
    return location;
  } catch (error) {
    console.error(
      '📍 getCurrentLocationFromGeolocation Error fetching current location:',
      error,
    );
    return null;
  }
};

/**
 * Get Current Location from device's geolocation service
 */
const getCurrentLocation = () => {
  try {
    Geolocation.getCurrentPosition(
      position => {
        console.info('📍 Current Location:', position);
        return position;
      },
      error => {
        console.error(
          '📍 getCurrentLocation Error fetching current location:',
          error,
        );
      },
      {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 10000,
        showLocationDialog: true,
      },
    );
  } catch (error) {
    console.error(
      '📍 getCurrentLocation catch Error fetching current location:',
      error,
    );
  }
};

/**
 * Initialize BackgroundFetch
 */
const initBackgroundFetch = async () => {
  console.info('Initializing BackgroundFetch');
  try {
    await BackgroundFetch.configure(
      {
        minimumFetchInterval: 15, // Fetch every 15 minutes
        stopOnTerminate: false,
        startOnBoot: true,
        forceAlarmManager: true,
        enableHeadless: true,
      },
      async taskId => {
        console.info('[BackgroundFetch] Event:', taskId);
        if (isHeartbeatEnabled) {
          const location = await getCurrentLocationFromGeolocation();
          if (location) {
            postLocation(location);
          }
        }
        BackgroundFetch.finish(taskId);
      },
      async taskId => {
        console.error('[BackgroundFetch] Timeout:', taskId);
        BackgroundFetch.finish(taskId);
      },
    );
    console.info('[BackgroundFetch] Configured');
  } catch (error) {
    console.error('[BackgroundFetch] Initialization Error:', error);
  }
};

const showMyConfirmDialog = ({
  title,
  text,
}: {
  title: string;
  text: string;
}) => {
  return new Promise<boolean>(resolve => {
    Alert.alert(title, text, [
      { text: 'Cancel', onPress: () => resolve(false), style: 'cancel' },
      { text: 'Confirm', onPress: () => resolve(true) },
    ]);
  });
};

const checkBatteryOptimization = async () => {
  const isEmulator: boolean = await DeviceInfo.isEmulator();
  if (isEmulator) {
    console.info('[BatteryOptimization] Skipped on emulator');
    return;
  }

  const isIgnoring =
    await BackgroundGeolocation.deviceSettings.isIgnoringBatteryOptimizations();
  console.info(
    '[BatteryOptimization] ignoringBatteryOptimizations:',
    isIgnoring,
  );

  if (!isIgnoring) {
    const request =
      await BackgroundGeolocation.deviceSettings.showIgnoreBatteryOptimizations();

    const confirmed = await showMyConfirmDialog({
      title: 'Battery Optimization',
      text: 'To ensure background tracking works reliably, please exclude RapidMedical from battery optimizations.',
    });

    if (confirmed) {
      await BackgroundGeolocation.deviceSettings.show(request);
    }
  }
};

const initBackgroundGeolocation = async () => {
  try {
    if (!hasAppStateListener) {
      AppState.addEventListener('change', nextAppState => {
        if (nextAppState === 'active') {
          if (Platform.OS === 'android') {
            checkBatteryOptimization();
          }
        }
      });
      hasAppStateListener = true;
    }

    const state = await BackgroundGeolocation.ready({
      desiredAccuracy: BackgroundGeolocation.DESIRED_ACCURACY_HIGH,
      distanceFilter: 0,
      disableElasticity: false,
      heartbeatInterval: HEARTBEAT_INTERVAL,
      stopTimeout: 5,
      logLevel: BackgroundGeolocation.LOG_LEVEL_ERROR,
      stopOnTerminate: false,
      startOnBoot: true,
      enableHeadless: true,
      preventSuspend: true,
      allowIdenticalLocations: true,
      disableLocationAuthorizationAlert: true,
      notification: {
        channelName: 'Location',
        priority: BackgroundGeolocation.NOTIFICATION_PRIORITY_HIGH,
        smallIcon: 'ic_launcher',
        color: '#DD2424',
        title: 'RapidMedical',
        text: 'RapidMedical is running',
        sticky: true,
        largeIcon: 'ic_launcher',
      },
    });

    console.info('[BackgroundGeolocation] Initialized: ', state.enabled);

    BackgroundGeolocation.onHeartbeat(async () => {
      console.info('-->onHeartbeat event');
      const taskId = await BackgroundGeolocation.startBackgroundTask();
      try {
        const location = await BackgroundGeolocation.getCurrentPosition({
          samples: 2,
          timeout: 10,
          extras: {
            event: 'heartbeat',
          },
        });
        postLocation(location, false, { event: 'heartbeat' });
      } catch (error) {
        console.info('[getCurrentPosition] ERROR: ', error);
      }
      BackgroundGeolocation.stopBackgroundTask(taskId);
    });

    BackgroundGeolocation.onLocation(
      location => {
        console.info('-->onLocation event');
        postLocation(location, false, { event: 'motion' });
      },
      error => {
        console.warn('[onLocation] ERROR: ', error);
      },
    );

    if (!state.enabled) {
      BackgroundGeolocation.start(() => {
        console.info('[BackgroundGeolocation] Start success');
      });
    }

    // Get first location as soon as we open the app & go to the route screen
    getCurrentLocation();
  } catch (error) {
    console.error('[BackgroundGeolocation] Initialization Error:', error);
  }
};

/**
 * Returns BackgroundGeolocation service's current state
 */
export const isBackgroundLocationEnabled = async () => {
  console.info('Cleaning up BackgroundGeolocation');
  const backgroundLocationState = await BackgroundGeolocation.getState();
  return backgroundLocationState.enabled;
};

/**
 * Clean up resources when needed (e.g., on routeId change)
 */
export const cleanupLocationService = async () => {
  console.info('Cleaning up BackgroundGeolocation');

  try {
    const state = await BackgroundGeolocation.getState();

    if (state.enabled) {
      await BackgroundGeolocation.stop();
      BackgroundGeolocation.removeListeners();
    }

    const fetchStatus = await BackgroundFetch.status();
    if (fetchStatus === BackgroundFetch.STATUS_AVAILABLE) {
      await BackgroundFetch.stop();
    }
  } catch (error) {
    console.error('Error while cleaning up BackgroundGeolocation:', error);
  }

  // Reset internal state
  routeId = undefined;
  stopId = undefined;
  dailyScheduleId = undefined;
  lockCheckInOrder = true;
  isHeartbeatEnabled = false;

  console.info('BackgroundGeolocation cleaned up and internal state reset');
};

export const submitCheckInLocation = async (): Promise<boolean> => {
  // NOTE: Instead of relying on the GPS timestamp, we can use the current time when the action is initiated.
  const timestamp = new Date().toISOString();
  const location = await BackgroundGeolocation.getCurrentPosition({
    samples: 1,
    persist: false,
    extras: { event: 'checkin' },
  });

  if (location) {
    const locationWithCurrentTime = {
      ...location,
      timestamp,
    };
    postLocation(locationWithCurrentTime, true, { event: 'checkin' });
    return true;
  }

  return false;
};
