import axios, {
  AxiosRequestConfig,
  AxiosResponse,
  RawAxiosRequestHeaders,
} from 'axios';
import { saveDataToRealm } from '~/db/realm';
import { SentryService } from '~/services/SentryService';
import LogoutService from '~/services/LogoutService';
import TokenService from '~/services/TokenService';

interface CustomHeaders extends RawAxiosRequestHeaders {
  'Auth-Request-Type'?: string;
  Authorization?: string;
}

const AUTH_HOST = process.env.AUTH_HOST;

const endpoints = {
  AUTH: `${AUTH_HOST}/services/oauth2/authorize`,
  REFRESH: `${AUTH_HOST}/services/oauth2/token`,
  REVOKE: `${AUTH_HOST}/services/oauth2/revoke`,
  REDIRECT: `${AUTH_HOST}/services/apexrest/auth/code-exchange/v1`,
};

const commonHeaders = { 'Content-Type': 'application/x-www-form-urlencoded' };

/**
 * AuthService handles authenticating the user with Salesforce.
 */
export const AuthService = {
  /**
   * Initiate the oauth 2.0 authorization flow to receive the correct credentials to communicate with Salesforce
   */
  login: async (email: string, password: string) => {
    const url = endpoints.AUTH;
    const encodedUserCredentials = btoa(email + ':' + password);

    const headers: CustomHeaders = {
      'Auth-Request-Type': 'Named-User',
      Accept: 'application/json',
      Authorization: `Basic ${encodedUserCredentials}`,
      ...commonHeaders,
    };

    const data: any = {
      response_type: 'code_credentials',
      redirect_uri: endpoints.REDIRECT,
      client_id: process.env.CLIENT_ID,
    };

    const config: AxiosRequestConfig = {
      headers: headers,
    };

    let authResponse: AxiosResponse | null = null;
    let userId: string | null = null;

    try {
      authResponse = await axios.post<AxiosResponse>(url, data, config);

      if (authResponse?.data) {
        const { id } = authResponse.data;
        await TokenService.storeTokens(authResponse.data);

        const idSplitStringArray: string[] = id.split('/');
        const idSplitStringArrayLength: number = idSplitStringArray.length;

        userId = idSplitStringArray[idSplitStringArrayLength - 1];
      }
    } catch (error) {
      throw error;
    }

    if (userId) {
      global.userId = userId;

      try {
        await saveDataToRealm<string>('userId', userId);
      } catch (error) {
        console.error(
          'AuthService: login(): saveDataToRealm(): userId ',
          error,
        );
      }

      try {
        SentryService.setSentryUser(email, userId);
      } catch (error) {
        console.error('AuthService: login(): setSentryUser(): ', error);
      }
    }
  },
  /**
   * Get a new refresh token using the expired token from previous auth state.
   */
  refreshToken: async (refreshToken: string) => {
    const url = endpoints.REFRESH;

    const data: any = {
      grant_type: 'refresh_token',
      client_id: process.env.CLIENT_ID,
      refresh_token: refreshToken,
    };

    const config: AxiosRequestConfig = {
      headers: commonHeaders,
    };

    let refreshResponse: AxiosResponse | null = null;

    try {
      refreshResponse = await axios.post<AxiosResponse>(url, data, config);
    } catch (error) {
      console.error(
        'AuthService: refreshToken():',
        JSON.stringify(error?.response?.data),
      );
      if (error?.response?.data?.error === 'invalid_grant') {
        console.info(
          'AuthService: refreshToken(): Access token has expired, logging user out',
        );
        // Log token expiration event
        await SentryService.logSentryError({
          error: new Error('Invalid grant - token expired'),
          tags: {
            file: 'AuthService.ts',
            function: 'refreshToken',
            eventType: 'logout_triggered',
            errorType: 'invalid_grant',
          },
          extra: {
            errorData: error?.response?.data,
            refreshTokenLength: refreshToken?.length || 0,
            globalUserId: !!global.userId,
          },
          contexts: {
            tokenExpiration: {
              source: 'auth_service_refresh',
              timestamp: new Date().toISOString(),
            },
          },
          level: 'warning',
        });
        await LogoutService.logoutWithoutDeletingData();
      } else {
        await SentryService.logSentryError({
          error: error,
          tags: {
            file: 'AuthService.ts',
            function: 'refreshToken',
            eventType: 'refresh_failed',
          },
          extra: {
            errorData: error?.response?.data,
            refreshTokenLength: refreshToken?.length || 0,
            errorMessage: error?.response?.data?.error || error?.message,
          },
          contexts: {
            tokenExpiration: {
              source: 'auth_service_refresh',
              timestamp: new Date().toISOString(),
            },
          },
          level: 'error',
        });
        return null;
      }
    }
    return refreshResponse?.data;
  },
  /**
   * Revoke the valid token the user has to gain access to the salesforce API.
   * Only executed when the user logs out of the application.
   */
  revoke: async (token: string) => {
    const url = endpoints.REVOKE;

    const data: any = {
      token,
    };

    const config: AxiosRequestConfig = {
      headers: commonHeaders,
    };

    try {
      await axios.post<AxiosResponse>(url, data, config);
    } catch (error) {
      console.error('AuthService: revoke(): ', error);
      return false;
    }

    return true;
  },
};
