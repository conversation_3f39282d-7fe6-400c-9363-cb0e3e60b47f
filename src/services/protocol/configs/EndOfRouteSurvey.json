{"id": "EndOfRouteSurvey", "label": "Survey - Check In", "description": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "version": "1.0.0", "protocolType": "routeComplete", "executionCriteria": {"all": [{"field": "Route_Summary__c.Status__c", "operator": "===", "value": "Complete"}, {"field": "Route_Summary__c.Require_End_of_Route_Survey__c", "operator": "===", "value": true}, {"field": "Route_Summary__c.Survey_Complete__c", "operator": "===", "value": false}]}, "steps": [{"stepId": "emptyCoolerInVehicleStep", "type": "page", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "1/4"}, "title": "Take a photo of empty cooler in vehicle", "components": [{"type": "Image", "key": "emptyCoolerInVehicleStepExamplePhotos", "options": {"label": "Example photos", "multiple": true, "readonly": true, "uris": ["assets/images/survey_empty_cooler.webp"]}}, {"type": "DisplayText", "key": "photoShouldBeClear", "options": {"type": "title", "wrapInCard": true, "content": "Photo should be clear"}}, {"type": "DisplayText", "key": "shouldCaptureEntireCooler", "options": {"type": "title", "wrapInCard": true, "content": "Should capture the entire cooler"}}], "actions": [{"label": "Take photo", "type": "capturePhoto", "targetStepId": "areaWhereCoolerIsStoredStep", "targetKey": "emptyCoolerInVehicleStepExamplePhotos", "linkedEntities": ["Route_Summary__c"], "options": {"icon": "camera", "iconPosition": "left", "cameraScreenTitle": "Take a photo of empty cooler"}}]}, {"stepId": "areaWhereCoolerIsStoredStep", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "2/4"}, "type": "page", "title": "Take photo of area where cooler is stored", "components": [{"type": "Image", "key": "areaWhereCoolerIsStoredPhotos", "options": {"label": "Example photos", "multiple": true, "readonly": true, "uris": ["assets/images/survey_cooler_area.webp", "assets/images/survey_cooler_area_1.webp"]}}, {"type": "DisplayText", "key": "photoShouldBeClear", "options": {"type": "title", "wrapInCard": true, "content": "Photo should be clear"}}, {"type": "DisplayText", "key": "shouldCaptureEntireCooler", "options": {"type": "title", "wrapInCard": true, "content": "Should capture the entire cooler"}}], "actions": [{"label": "Take photo", "type": "capturePhoto", "linkedEntities": ["Route_Summary__c"], "targetKey": "areaWhereCoolerIsStoredPhotos", "targetStepId": "areaWhereSamplesAreHandledStep", "options": {"icon": "camera", "iconPosition": "left", "cameraScreenTitle": "Take a photo of where cooler is stored"}}]}, {"stepId": "areaWhereSamplesAreHandledStep", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "3/4"}, "type": "page", "title": "Take photo of area where samples are handled", "components": [{"type": "Image", "key": "areaWhereSamplesAreHandledPhotos", "options": {"label": "Example photos", "multiple": true, "readonly": true, "uris": ["assets/images/survey_area_specimens_handled.webp"]}}, {"type": "DisplayText", "key": "areaWhereSamplesAreHandledTitle", "options": {"type": "title", "wrapInCard": true, "content": "Front driver or passenger seat area"}}], "actions": [{"label": "Take photo", "type": "capturePhoto", "linkedEntities": ["Route_Summary__c"], "targetKey": "areaWhereSamplesAreHandledPhotos", "targetStepId": "protocolCompleteConfirmationStep", "options": {"icon": "camera", "iconPosition": "left", "cameraScreenTitle": "Take photo of the area where samples are handled"}}]}, {"stepId": "protocolCompleteConfirmationStep", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "4/4"}, "type": "page", "title": "Confirm that the following route protocols were completed", "components": [{"type": "CheckboxGroup", "key": "protocolCompleteConfirmationStep", "validations": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 4, "validationErrorMsg": "Please confirm all items before proceeding"}], "options": {"wrapInCard": false, "style": {"backgroundColor": "#ffffff"}, "label": "Confirm that the following route protocols were completed", "choices": [{"label": "No customer sample was left behind", "value": "noCustomerSampleLeftBehind"}, {"label": "Samples were not left in vehicles for over 12 hours", "value": "samplesNotLeftInVehiclesForOver12Hours"}, {"value": "youAreResponsibleForDeliveringAllPickups", "label": "You are responsible for delivering all pickups"}, {"value": "leavingPropertyBehindMayResultInPenalties", "label": "Leaving property behind may result in penalties"}]}}], "actions": [{"label": {"enabled": "Complete route survey", "disabled": "Confirm protocols to complete "}, "type": "invokeAction", "actionId": "completeRouteSurvey", "options": {"requiresValidation": true}}]}], "actions": {"completeRouteSurvey": {"type": "save", "after": {"actionId": "confirmation", "options": {"type": "route", "subType": "survey_completed"}}, "before": [{"type": "transform", "operation": "mapValue", "sourceFields": [{"condition": {"all": [{"field": "protocolCompleteConfirmationStep", "operator": "custom", "customOperator": "includes", "value": "noCustomerSampleLeftBehind"}, {"field": "protocolCompleteConfirmationStep", "operator": "custom", "customOperator": "includes", "value": "samplesNotLeftInVehiclesForOver12Hours"}, {"field": "protocolCompleteConfirmationStep", "operator": "custom", "customOperator": "includes", "value": "youAreResponsibleForDeliveringAllPickups"}, {"field": "protocolCompleteConfirmationStep", "operator": "custom", "customOperator": "includes", "value": "leavingPropertyBehindMayResultInPenalties"}]}, "value": true}], "destinationField": "Route_Summary__c.Survey_Complete__c"}], "objects": [{"name": "Route_Summary__c", "fields": ["Survey_Complete__c"], "action": "update"}]}}}