{"id": "Hot_Weather_Checklist", "label": "Heat protocol - Pre-route", "version": "1.0.0", "description": "Must be completed before route start", "icon": "hotWeather", "protocolType": "beginningOfDailySchedule", "executionCriteria": {"all": [{"field": "Route_Summary__c.High_Temperature__c", "operator": ">=", "value": 75}]}, "steps": [{"stepId": "hotWeatherLanding", "config": {"scrollable": false}, "type": "page", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "1/3"}, "components": [{"type": "Icon", "key": "gridViewIcon", "options": {"name": "gridView", "style": {"alignSelf": "center", "height": "50%", "justifyContent": "center", "alignItems": "center"}}}, {"type": "Icon", "key": "hotWeatherIcon", "options": {"name": "hotWeather", "style": {"alignSelf": "center", "marginTop": "-38%", "marginBottom": "10%"}}}, {"type": "DisplayText", "key": "hotWeatherInstruction", "options": {"type": "title", "align": "center", "wrapInCard": false, "content": "Hot weather protocol"}}, {"type": "DisplayText", "key": "supplyChecklistInstruction", "options": {"type": "subtitle", "align": "center", "wrapInCard": false, "content": "Weather protocols are in place. Follow instructions for specimen integrity"}}, {"type": "ExpectedTemperature", "key": "expectedTemperature", "options": {"location": "Stop__c.City__c", "temperature": "Route_Summary__c.High_Temperature__c", "icon": "redThermometer", "style": {"position": "absolute", "bottom": 10, "left": 19, "right": 19}}}], "actions": [{"label": "Confirm", "type": "navigate", "targetStepId": "supplyChecklistStep"}]}, {"stepId": "supplyChecklistStep", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "2/3"}, "type": "page", "title": "Supply Checklist", "components": [{"type": "CheckboxGroup", "key": "supplyChecklistItems", "validations": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 3, "validationErrorMsg": "Please confirm all items before proceeding"}], "options": {"wrapInCard": false, "style": {"backgroundColor": "#ffffff"}, "label": "Required Supplies", "choices": [{"label": "At least 10 frozen ice packs", "value": "frozenIcePacks"}, {"label": "Disposable insulated pads", "value": "insulatedPads"}, {"label": "Insulated cooler", "value": "insulatedCooler"}]}}], "actions": [{"label": {"enabled": "Continue", "disabled": "Complete all steps to continue"}, "type": "navigate", "targetStepId": "hotWeatherAdditionalProtocolsStep", "options": {"requiresValidation": true}}]}, {"stepId": "hotWeatherAdditionalProtocolsStep", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "3/3"}, "type": "page", "title": "Additional Protocols", "components": [{"type": "CheckboxGroup", "key": "additionalProtocolsItems", "validations": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 3, "validationErrorMsg": "Please confirm all items before proceeding"}], "options": {"wrapInCard": false, "style": {"backgroundColor": "#ffffff"}, "label": "Additional Protocols", "choices": [{"label": "Read package temperature at each location", "value": "readTemperature"}, {"label": "Maintaining specimen integrity", "value": "specimenIntegrity"}, {"label": "Complete additional steps during the end-of-route survey", "value": "completeEndOfRouteSurvey"}]}}], "actions": [{"label": {"enabled": "Continue", "disabled": "Complete all steps to continue"}, "type": "invokeAction", "actionId": "completeHotWeatherChecklist", "options": {"requiresValidation": true}}]}], "actions": {"completeHotWeatherChecklist": {"type": "save", "after": {"actionId": "confirmation", "options": {"type": "protocol", "subType": "protocol_completed"}}, "before": [{"type": "transform", "operation": "mapValue", "sourceFields": [{"condition": {"all": [{"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "readTemperature"}, {"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "specimenIntegrity"}, {"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "completeEndOfRouteSurvey"}]}, "value": "a3ZTR000000D0Td2AK"}], "destinationField": "Protocol_Verification__c.Protocol__c"}, {"type": "transform", "operation": "mapValue", "sourceFields": [{"condition": {"all": [{"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "readTemperature"}, {"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "specimenIntegrity"}, {"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "completeEndOfRouteSurvey"}]}, "value": "Complete"}], "destinationField": "Protocol_Verification__c.Field_Value__c"}, {"type": "transform", "operation": "mapValueFromContext", "sourceFields": [{"condition": {"all": [{"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "readTemperature"}, {"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "specimenIntegrity"}, {"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "completeEndOfRouteSurvey"}]}, "value": "Daily_Schedule__c.Id"}], "destinationField": "Protocol_Verification__c.Daily_Schedule__c"}, {"type": "transform", "operation": "mapValue", "sourceFields": [{"condition": {"all": [{"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "readTemperature"}, {"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "specimenIntegrity"}, {"field": "additionalProtocolsItems", "operator": "custom", "customOperator": "includes", "value": "completeEndOfRouteSurvey"}]}, "value": "hotWeatherAdditionalProtocolsStep"}], "destinationField": "Protocol_Verification__c.Protocol_Step_Id__c"}], "objects": [{"name": "Protocol_Verification__c", "fields": ["Daily_Schedule__c", "Field_Value__c", "Protocol__c", "Protocol_Step_Id__c", "Id"], "action": "create"}]}}}