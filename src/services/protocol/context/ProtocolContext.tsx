import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useNavigation } from '@react-navigation/native';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from 'react';
import { ProtocolEngineStackParams } from '~/navigation/ProtocolEngineStack';
import { Protocol } from '~/types/protocol.types';
import { ProtocolContextData, StepAction } from '~/types/protocolActions.types';
import {
  ActionHandlerResult,
  processStepAction,
} from '~/services/protocol/ProtocolActionService';
import { useProtocolDataStore } from '~/services/protocol/context/ProtocolDataStore';
import { checkCameraPermissionsAndOpenIt } from '~/components/camera/CheckPermissionAndOpenCamera';
import { ImageTitleType } from '~/services/ImageStoreService';
import { extractLinkedEntityIds } from '~/services/protocol/ProtocolScenarioDataService';
import { handleSaveProtocolAction } from '~/services/protocol/ProtocolDataSyncService';
import { StepType } from '~/types/step.types';

export type ProtocolEngineStackNavigationProp =
  NativeStackNavigationProp<ProtocolEngineStackParams>;

type StepActionWithStepType = StepAction & {
  stepType?: StepType;
};
export type ProtocolContextType = {
  scenario: ProtocolContextData;
  handleAction: (action: StepActionWithStepType) => void;
  setNestedNavigationRef: (ref: ProtocolEngineStackNavigationProp) => void;
};

export const ProtocolContext = createContext<ProtocolContextType>({
  scenario: {},
  handleAction: () => {},
  setNestedNavigationRef: () => {},
});

export const ProtocolScenarioProvider = ({
  children,
  scenario,
  protocol,
}: {
  children: React.ReactNode;
  scenario: Record<string, any>;
  protocol: Protocol;
}) => {
  const currentStepIndex = useRef<number | null>(null);
  const nestedNavigationRef = useRef<ProtocolEngineStackNavigationProp | null>(
    null,
  );
  const navigation = useNavigation();

  const handleStateChange = useCallback(
    (e: { data: { state: { index: number } } }) => {
      currentStepIndex.current = e.data.state.index;
    },
    [],
  );

  useEffect(() => {
    return () => {
      nestedNavigationRef.current?.removeListener('state', handleStateChange);
    };
  }, [handleStateChange]);

  const setNestedNavigationRef = useCallback(
    (ref: ProtocolEngineStackNavigationProp) => {
      nestedNavigationRef.current = ref;
      nestedNavigationRef.current?.addListener('state', handleStateChange);
    },
    [handleStateChange],
  );

  const handleNavigateAction = useCallback(
    (targetStepId: string) => {
      const index = protocol.steps.findIndex(
        step => step.stepId === targetStepId,
      );

      if (index === -1) {
        console.warn(
          `ProtocolContext: handleAction: NAVIGATE: targetStepId: ${targetStepId} not found`,
        );
        return;
      }

      const step = protocol.steps[index];
      const screenName = step.type === 'modal' ? 'StepModal' : 'StepPage';

      const isForwardNavigation =
        currentStepIndex.current === null || index > currentStepIndex.current;

      if (isForwardNavigation) {
        const isCurrentStepModal =
          typeof currentStepIndex.current === 'number' &&
          protocol.steps[currentStepIndex.current].type === 'modal';

        if (isCurrentStepModal) {
          nestedNavigationRef.current?.goBack();
          setTimeout(() => {
            nestedNavigationRef.current?.push(screenName, { step });
          }, 100);
        } else {
          nestedNavigationRef.current?.push(screenName, { step });
        }
      } else {
        const screensToPop =
          currentStepIndex.current === null
            ? 1
            : currentStepIndex.current - index;
        nestedNavigationRef.current?.pop(screensToPop);
      }

      currentStepIndex.current = index;
    },
    [protocol.steps],
  );

  const handleAction = useCallback(
    async (action: StepActionWithStepType) => {
      const { userData, setUserData } = useProtocolDataStore.getState();

      const result: ActionHandlerResult = processStepAction(
        action,
        protocol.actions,
        { ...scenario, ...userData },
      );

      switch (result.status) {
        case 'exit':
          currentStepIndex.current = null;
          nestedNavigationRef.current?.popToTop();
          nestedNavigationRef.current?.goBack();
          break;

        case 'close':
          currentStepIndex.current = null;
          nestedNavigationRef.current?.goBack();
          break;

        case 'navigate':
          if (result.nextStepId === null) {
            console.warn('handleAction: NAVIGATE: targetStepId is undefined');
            return;
          }
          handleNavigateAction(result.nextStepId);
          break;

        case 'invokeAction':
          // NOTE: Handle action invocation
          // console.info('invoke action', JSON.stringify(result, null, 2));
          break;

        case 'save': {
          if (action.stepType === StepType.MODAL) {
            nestedNavigationRef.current?.goBack();
          }

          const isSuccess = await handleSaveProtocolAction(result, scenario);

          if (!result.after) {
            console.warn(
              'ProtocolContext: handleAction: save: No after action defined',
            );
            return;
          }

          if (isSuccess) {
            const { actionId, options } = result.after;

            if (actionId === 'confirmation') {
              const { type, subType } = options;

              navigation.navigate('ConfirmationScreen', {
                type,
                subType,
              });
            }
          }
          break;
        }

        case 'capturePhoto': {
          const onCapture = async (_imageBase64: string) => {
            if (!_imageBase64) {
              return;
            }

            const { linkedEntities = [], targetKey = '' } = action;

            if (linkedEntities.length === 0) {
              console.warn(
                `handleAction: ${result.status}: No linkedEntities defined`,
              );
              return;
            }

            const { Route_Summary__c } = extractLinkedEntityIds(
              scenario,
              linkedEntities,
            );

            if (!Route_Summary__c) {
              console.warn(
                `handleAction: ${result.status}: Route_Summary__c is undefined`,
              );
              return;
            }
            const imageTitleKey = targetKey as ImageTitleType;

            const newImageMetadata = {
              titleType: imageTitleKey,
              routeSummaryId: Route_Summary__c,
              base64String: _imageBase64,
            };

            const updatedImagesToUpload = {
              ...userData.imagesToUpload,
              [imageTitleKey]: newImageMetadata,
            };

            setUserData('imagesToUpload', updatedImagesToUpload);

            if (result.nextStepId === null) {
              console.warn('handleAction: NAVIGATE: targetStepId is undefined');
              return;
            }

            handleNavigateAction(result.nextStepId);
          };

          await checkCameraPermissionsAndOpenIt(
            nestedNavigationRef.current,
            onCapture,
            {
              title: action.options?.cameraScreenTitle ?? '',
            },
          );
          break;
        }
        default:
          console.warn(`handleAction: ${result.status} yet to be implemented`);
          break;
      }
    },
    [protocol.actions, handleNavigateAction, scenario, navigation],
  );

  const value = useMemo(
    () => ({
      scenario,
      handleAction,
      setNestedNavigationRef,
    }),
    [scenario, handleAction, setNestedNavigationRef],
  );

  return (
    <ProtocolContext.Provider value={value}>
      {children}
    </ProtocolContext.Provider>
  );
};

export const useProtocolContext = (): ProtocolContextType => {
  const context = useContext(ProtocolContext);
  if (!context) {
    throw new Error('useProtocolContext must be used within a ProtocolContext');
  }
  return context;
};
