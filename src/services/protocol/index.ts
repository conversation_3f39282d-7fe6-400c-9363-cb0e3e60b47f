import { Protocol, ProtocolType } from '~/types/protocol.types';
import { doesConditionHold } from '~/services/protocol/ProtocolConditionEvaluatorService';
import { StopWithRelations } from '~/types/stops.types';
import StopClosureDelayProtocol from '~/services/protocol/configs/StopClosureDelayProtocol.json';
import SNOProtocol from '~/services/protocol/configs/SNOProtocol.json';
import EndOfRouteSurvey from '~/services/protocol/configs/EndOfRouteSurvey.json';
import GeofenceProtocol from '~/services/protocol/configs/GeofenceProtocol.json';
import HeatProtocol from '~/services/protocol/configs/HeatProtocol.json';
import { ProtocolContextData } from '~/types/protocolActions.types';
import { buildScenario } from '~/services/protocol/ProtocolScenarioDataService';

interface ProtocolResult {
  protocol: Protocol;
  scenario: ProtocolContextData;
}

interface ProtocolCheckResult {
  isLoading: boolean;
  protocolData: ProtocolResult | null;
  error: Error | null;
}

const protocols = {
  [ProtocolType.PRE_ARRIVE_STOP]: StopClosureDelayProtocol,
  [ProtocolType.PRE_COMPLETE_STOP]: SNOProtocol,
  [ProtocolType.POST_ROUTE_COMPLETE]: EndOfRouteSurvey,
  [ProtocolType.POST_ARRIVE_STOP]: GeofenceProtocol,
  [ProtocolType.PRE_ROUTE_START]: HeatProtocol,
};

const FeedsProtocolTypes = [ProtocolType.POST_ROUTE_COMPLETE];

/**
 * Check if a protocol is applicable based on the context and the protocol type.
 * @param type - The type of protocol to check. Eg. PRE_ARRIVE_STOP, PRE_COMPLETE_STOP, POST_ROUTE_COMPLETE
 * @param context - The context to check the protocol against.
 * @returns An object containing the applicable protocol and the context if it is applicable, otherwise null.
 */
const getApplicableProtocol = ({
  type,
  scenario,
}: {
  type: ProtocolType;
  scenario: ProtocolContextData;
}): Protocol | null => {
  const protocol = protocols[type];

  if (!protocol) {
    return null;
  }

  const shouldExecute = doesConditionHold(protocol.executionCriteria, scenario);

  if (shouldExecute) {
    return protocol;
  }
  return null;
};

const getNextApplicableProtocol = async ({
  stopId,
  routeSummaryId,
}: {
  stopId: string;
  routeSummaryId: string;
}): Promise<{ protocol: Protocol; scenario: ProtocolContextData } | null> => {
  const scenario = await buildScenario({ stopId, routeSummaryId });

  if (!scenario) {
    return null;
  }

  for (const type of FeedsProtocolTypes) {
    const protocol = getApplicableProtocol({ type, scenario });

    if (protocol) {
      return { protocol, scenario };
    }
  }

  return null;
};

/**
 * Checks if a hot weather protocol is applicable for the given stop
 * @param stop The stop to check protocols for
 * @returns Promise<ProtocolCheckResult> The result of the protocol check
 */
const checkHotWeatherProtocol = async (
  stop: StopWithRelations,
): Promise<ProtocolCheckResult> => {
  if (!stop) {
    return {
      isLoading: false,
      protocolData: null,
      error: null,
    };
  }

  try {
    const scenario = await buildScenario({
      stopId: stop.Id,
      routeSummaryId: stop.Summary__c,
      includeDailySchedule: true,
    });

    if (!scenario) {
      return {
        isLoading: false,
        protocolData: null,
        error: null,
      };
    }

    const protocol = ProtocolService.getApplicableProtocol({
      type: ProtocolType.PRE_ROUTE_START,
      scenario,
    });

    if (!protocol) {
      return {
        isLoading: false,
        protocolData: null,
        error: null,
      };
    }

    return {
      isLoading: false,
      protocolData: { protocol, scenario },
      error: null,
    };
  } catch (error) {
    console.error('Error checking hot weather protocol:', error);
    return {
      isLoading: false,
      protocolData: null,
      error: error as Error,
    };
  }
};

const isStopEligibleForHeatProtocol = (stop: StopWithRelations): boolean => {
  return Boolean(stop && !stop.Arrival_Time__c);
};

export const ProtocolService = {
  getApplicableProtocol,
  getNextApplicableProtocol,
  isStopEligibleForHeatProtocol,
  checkHotWeatherProtocol,
};
