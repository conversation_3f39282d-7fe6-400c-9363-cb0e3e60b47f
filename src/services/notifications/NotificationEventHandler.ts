import { AdditionalData } from '~/types/notifications.types';

export const handleNewRouteNotificationEvent = async (
  additionalData: AdditionalData,
  isBackgroundNotification = false,
) => {
  if (
    additionalData?.data?.routeName &&
    additionalData.data?.routeId &&
    !isBackgroundNotification
  ) {
    // TODO: Implement
  }
};

export const handleStopActionNotificationEvent = async (
  additionalData: AdditionalData,
  isBackgroundNotification = false,
) => {
  if (
    !isBackgroundNotification &&
    additionalData?.data?.routeName &&
    additionalData.data?.routeId &&
    additionalData.data?.stopId &&
    additionalData.data?.stopName
  ) {
    // TODO: Implement
  }
};

export const handleStopCancelledNotificationEvent = async (
  additionalData: AdditionalData,
  isBackgroundNotification = false,
) => {
  if (additionalData?.data.stopName && !isBackgroundNotification) {
    // TODO: Implement
  }
};

export const handleResyncNotificationEvent = async (
  additionalData: AdditionalData,
  isBackgroundNotification = false,
) => {
  if (!isBackgroundNotification) {
    // TODO: Implement
  }
};
