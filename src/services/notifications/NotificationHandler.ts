import {
  isNewRouteNotification,
  isRouteReadyNotification,
  isNewStopAddedNotification,
  isNotesUpdatedForStop,
  isPreferredTimeUpdatedForStop,
  isStopCancelledNotification,
  isResyncDataNotification,
} from '~/services/notifications/NotificationTypeCheck';
import {
  handleNewRouteNotificationEvent,
  handleStopActionNotificationEvent,
  handleStopCancelledNotificationEvent,
  handleResyncNotificationEvent,
} from '~/services/notifications/NotificationEventHandler';

export const handleNotificationOpenedData = (openedEvent: any) => {
  const openedEventAdditionalData = openedEvent.notification.additionalData;

  switch (true) {
    case isNewRouteNotification(openedEventAdditionalData):
    case isRouteReadyNotification(openedEventAdditionalData):
      handleNewRouteNotificationEvent(openedEventAdditionalData, true);
      break;

    case isNewStopAddedNotification(openedEventAdditionalData):
    case isNotesUpdatedForStop(openedEventAdditionalData):
    case isPreferredTimeUpdatedForStop(openedEventAdditionalData):
      handleStopActionNotificationEvent(openedEventAdditionalData, true);
      break;

    case isStopCancelledNotification(openedEventAdditionalData):
      handleStopCancelledNotificationEvent(openedEventAdditionalData, true);
      break;

    case isResyncDataNotification(openedEventAdditionalData):
      handleResyncNotificationEvent(openedEventAdditionalData, true);
      break;

    default:
      break;
  }
};

export const handleForegroundNotification = (
  notificationReceivedEvent: any,
) => {
  const notification = notificationReceivedEvent.getNotification();
  // silence notification when app is in foreground
  notificationReceivedEvent.complete(undefined);
  const additionalData = notification.additionalData;

  switch (true) {
    case isNewRouteNotification(additionalData):
    case isRouteReadyNotification(additionalData):
      handleNewRouteNotificationEvent(additionalData);
      break;

    case isNewStopAddedNotification(additionalData):
    case isNotesUpdatedForStop(additionalData):
    case isPreferredTimeUpdatedForStop(additionalData):
      handleStopActionNotificationEvent(additionalData);
      break;

    case isStopCancelledNotification(additionalData):
      handleStopCancelledNotificationEvent(additionalData);
      break;

    case isResyncDataNotification(additionalData):
      handleResyncNotificationEvent(additionalData);
      break;

    default:
      break;
  }
};
