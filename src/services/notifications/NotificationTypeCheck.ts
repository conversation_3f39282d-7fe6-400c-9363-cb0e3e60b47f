import {
  AdditionalData,
  NotificationType,
  NewMessageReceivedAdditionalData,
} from '~/types/notifications.types';

/* export type NotificationType =
  | 'new_message'
  | 'new_stop_added_to_route'
  | 'end_of_survey'
  | 'new_route'
  | 'route_is_ready';
 */
/**
 *  "Your X route is ready!" I need the Name property of that Route.
 *   But depending on the information we'd like to display in the in app alert,
 *   we might need to add more properties, such as createdBy to achieve in app message like following:
 *   "Your X Route Created By Y Person is Ready".
 *   Therefore, in short words: notification payload data will shape on what kind of informations
 *   we'd like to display in the app as an alert.
 */
/* const newRouteAssignedNotificationPayload: NotificationPayload = {
  type: 'new_route',
  data: {
    routeName: 'Jacobs Route',
    createdBy: 'Jacob',
  },
}; */

export const isNewRouteNotification = (additionalData: AdditionalData) => {
  return additionalData?.type === NotificationType.newRouteText;
};

export const isRouteReadyNotification = (additionalData: AdditionalData) => {
  return additionalData?.type === NotificationType.routeReadyText;
};

export const isNewStopAddedNotification = (additionalData: AdditionalData) => {
  return additionalData?.type === NotificationType.newStopAddedText;
};

export const isNotesUpdatedForStop = (additionalData: AdditionalData) => {
  return additionalData?.type === NotificationType.stopNotesUpdatedText;
};

export const isPreferredTimeUpdatedForStop = (
  additionalData: AdditionalData,
) => {
  return additionalData?.type === NotificationType.stopRescheduledText;
};

export const isStopCancelledNotification = (additionalData: AdditionalData) => {
  return additionalData?.type === NotificationType.stopCancelledText;
};

export const isResyncDataNotification = (additionalData: AdditionalData) => {
  return additionalData?.type === NotificationType.autoSyncUp;
};

export const isNewChatMessageNotification = (
  additionalData: AdditionalData,
): additionalData is NewMessageReceivedAdditionalData => {
  return additionalData?.typeName === NotificationType.addMessageText;
};

export const isNewChatOpenedNotification = (
  additionalData: AdditionalData,
): additionalData is NewMessageReceivedAdditionalData => {
  return additionalData?.typeName === NotificationType.openChatText;
};

export const isEndOfSurveyNotification = (additionalData: AdditionalData) => {
  return additionalData?.type === 'end_of_survey';
};
