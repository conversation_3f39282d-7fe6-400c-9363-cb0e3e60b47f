interface QueryConfig {
  fields: readonly string[];
  objectName: string;
  whereClause: string;
  orderBy?: string;
  limit?: number;
}

interface NestedQueryConfig {
  mainFields: readonly string[];
  stopFields: readonly string[];
  serviceFields: readonly string[];
  parcelFields: readonly string[];
  whereClause: string;
}

export const API_ENDPOINT_KEYS = {
  GET_ROUTE_LIST: 'getRouteList',
  GET_STOP_LIST: 'getStopList',
  GET_CONTACT_BY_USER_ID: 'getContactByUserId',
  GET_TODAYS_SCHEDULE: 'getTodaysSchedule',
  GET_PARCEL_TYPES: 'getParcelTypes',
  GET_SERVICE_TYPES: 'getServiceTypes',
  POST_HEARTBEAT_LOCATION: 'postHeartbeatLocation',
  POST_BATCH_SOBJECTS: 'postBatchSobjects',
  POST_CONTENT_VERSION: 'postContentVersion',
  PATCH_STOP: 'patchStop',
  PATCH_ROUTE_SUMMARY: 'patchRouteSummary',
  GET_PROTOCOL_VERIFICATIONS: 'getProtocolVerifications',
} as const;

const ROUTE_SUMMARY_FIELDS = [
  'LastModifiedDate',
  'Id',
  'Name',
  'Planned_Start__c',
  'Planned_End__c',
  'Date__c',
  'Number_of_Stops__c',
  'Status__c',
  'Route__r.Name',
  'SNO_Flow__c',
  'UTC_Offset__c',
  'Rapid_Closure_Warning__c',
  'Default_Delivery_Destination__c',
  'Survey_Complete__c',
  'Require_End_of_Route_Survey__c',
  'Geofencing_Distance__c',
  'High_Temperature__c',
  'Lock_Check_In_Order__c',
] as const;

const STOP_FIELDS = [
  'Id',
  'Name',
  'Stop_Time_Preferred__c',
  'Stop_Time_Min__c',
  'Stop_Time_Max__c',
  'ETA__c',
  'Type__c',
  'Address__c',
  'Address_1__c',
  'Address_2__c',
  'City__c',
  'Postal_Code__c',
  'State__c',
  'Status__c',
  'Stop_Coordinates__Latitude__s',
  'Stop_Coordinates__Longitude__s',
  'Summary__c',
  'Notes__c',
  'Coordinates__c',
  'Arrival_Time__c',
  'Completed_Time__c',
  'Geofencing_Distance__c',
  'Service_Comments__c',
  'Pieces__c',
  'Pieces_Delivered__c',
  'Expected_Dropoff_Quantity__c',
  'Location__r.Arrival_Verification_Method__c',
  'Location__r.name',
  'Location__r.Coordinates__Latitude__s',
  'Location__r.Coordinates__Longitude__s',
  'Location__r.Geofencing_Distance__c',
  'SNO_Bypass_DateTime__c',
  'SNO_Driver_Waiting__c',
  'Proof_of_Service__c',
  'Post_Date__c',
  'Daily_Schedule__c',
] as const;

const SCHEDULE_FIELDS = [
  'Id',
  'Name',
  'Date__c',
  'Driver__c',
  'Driver_User_Id__c',
  'High_Temperature__c',
] as const;

const SERVICE_FIELDS = [
  'Id',
  'Name',
  'Stop__c',
  'Daily_Schedule__c',
  'Service_Type_Name__c',
  'Completed_Time__c',
] as const;

const CONTACT_FIELDS = [
  'Id',
  'Name',
  'Email',
  'OwnerId',
  'AccountId',
  'Salesforce_Account__c',
] as const;

const PARCEL_FIELDS = [
  'Id',
  'Name',
  'Requires_Signature__c',
  'Reference_Required__c',
  'Parcel_Type_Definition__c',
  'Parcel_Type_Name__c',
  'Pickup__c',
  'Delivery__c',
  'Signature__c',
  'Signature_Comments__c',
  'Signature_Statuses__c',
  'Quantity__c',
  'Quantity_Expected__c',
  'Dropoff_Quantity__c',
  'Comments__c',
  'Reference__c',
];

const PROTOCOL_VERIFICATION_FIELDS = [
  'Id',
  'Protocol_Step_Id__c',
  'Protocol__c',
  'Field_Value__c',
  'Daily_Schedule__c',
] as const;

const PARCEL_SHIPMENT_SERVICE_TYPE_FIELDS = ['Id', 'Name'];

export const API_ENDPOINTS: Record<string, string> = {
  getRouteList: generateSoqlQuery({
    fields: ROUTE_SUMMARY_FIELDS,
    objectName: 'Route_Summary__c',
    whereClause: 'Date__c+%3E%3D+LAST_N_DAYS%3A1',
  }),
  getContactByUserId: generateSoqlQuery({
    fields: CONTACT_FIELDS,
    objectName: 'Contact',
    whereClause: 'Salesforce_Account__c+%3D+%27{userId}%27',
  }),
  getServiceTypes: generateSoqlQuery({
    fields: PARCEL_SHIPMENT_SERVICE_TYPE_FIELDS,
    objectName: 'Service_Type__c',
    whereClause: 'Account__c+%3D+%27{accountId}%27',
  }),
  getParcelTypes: generateSoqlQuery({
    fields: PARCEL_SHIPMENT_SERVICE_TYPE_FIELDS,
    objectName: 'Parcel_Type__c',
    whereClause: 'Account__c+%3D+%27{accountId}%27',
  }),
  getTodaysSchedule: generateDailyScheduleQuery({
    mainFields: SCHEDULE_FIELDS,
    stopFields: STOP_FIELDS,
    serviceFields: SERVICE_FIELDS,
    parcelFields: PARCEL_FIELDS,
    whereClause:
      'Date__c+%3E%3D+LAST_N_DAYS%3A1+AND+Driver_User_Id__c+%3D+%27{userId}%27',
  }),
  getProtocolVerifications: generateSoqlQuery({
    fields: PROTOCOL_VERIFICATION_FIELDS,
    objectName: 'Protocol_Verification__c',
    whereClause: 'Daily_Schedule__r.Driver_User_Id__c+%3D+%27{userId}%27',
  }),

  postHeartbeatLocation: 'sobjects/Location_History__c',
  postBatchSobjects: 'composite/sobjects/',
  postContentVersion: 'sobjects/ContentVersion',
  patchStop: 'sobjects/Stop__c/{entityId}',
  patchRouteSummary: 'sobjects/Route_Summary__c/{entityId}',
};

function generateSoqlQuery({
  fields,
  objectName,
  whereClause,
  orderBy = 'LastModifiedDate',
  limit = 200,
}: QueryConfig): string {
  const selectClause = fields.join('%2C');
  return `query?q=select+${selectClause}+from+${objectName}+where+${whereClause}+order+by+${orderBy}+limit+${limit}`;
}

function generateDailyScheduleQuery({
  mainFields,
  stopFields,
  serviceFields,
  parcelFields,
  whereClause,
}: NestedQueryConfig): string {
  // URL encode the fields
  const encodeFields = (fields: readonly string[]) => fields.join('%2C');

  // Generate the nested service query
  const serviceQuery = `SELECT+${encodeFields(serviceFields)}+FROM+Services__r`;

  // Generate the nested pickup parcels query
  const pickupParcelsQuery = `SELECT+${encodeFields(parcelFields)}+FROM+Pickup_Parcels__r`;

  // Generate the nested delivery parcels query
  const deliveryParcelsQuery = `SELECT+${encodeFields(parcelFields)}+FROM+Delivery_Parcels__r`;

  // Generate the stops query with nested queries
  const stopsQuery = `SELECT+${encodeFields(stopFields)}%2C%28${serviceQuery}%29%2C%28${pickupParcelsQuery}%29%2C%28${deliveryParcelsQuery}%29+FROM+Stops__r+ORDER+BY+Stop_Time_Preferred__c`;

  // Generate the main query
  const mainQuery = `SELECT+${encodeFields(mainFields)}%2C%28${stopsQuery}%29+FROM+Daily_Schedule__c+WHERE+${whereClause}`;

  return `query?q=${mainQuery}`;
}
