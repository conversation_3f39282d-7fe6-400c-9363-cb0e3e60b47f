import React from 'react';
import { render, act, fireEvent, waitFor } from '@testing-library/react-native';
import StopActionButton from '~/components/buttons/StopActionButton';
import { Stop } from '~/types/stops.types';
import en from '~/localization/en';
import { STOP_STATUS, ROUTE_STATUS } from '~/utils/constants';
import { ProtocolService } from '~/services/protocol';
import { buildScenario } from '~/services/protocol/ProtocolScenarioDataService';
import { buildStopTasks } from '~/utils/stops';

const mockNavigate = jest.fn();

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

jest.mock('~/assets/icons', () => ({
  StopWatch: function MockStopWatch() {
    return 'MockStopWatch';
  },
}));

jest.mock('~/services/protocol/ProtocolScenarioDataService', () => ({
  buildScenario: jest.fn(),
}));

jest.mock('~/services/protocol', () => ({
  ProtocolService: {
    getApplicableProtocol: jest.fn(),
  },
}));

// Mock react-native-background-geolocation
jest.mock('react-native-background-geolocation', () => ({
  __esModule: true,
  default: {
    getCurrentPosition: jest.fn(),
    configure: jest.fn(),
    start: jest.fn(),
    stop: jest.fn(),
    onLocation: jest.fn(),
    onMotionChange: jest.fn(),
    onActivityChange: jest.fn(),
    onProviderChange: jest.fn(),
    onHeartbeat: jest.fn(),
    onHttp: jest.fn(),
    onError: jest.fn(),
    onGeofence: jest.fn(),
    onGeofencesChange: jest.fn(),
    onSchedule: jest.fn(),
    onPowerSaveChange: jest.fn(),
    onConnectivityChange: jest.fn(),
    onAuthorization: jest.fn(),
    onEnabledChange: jest.fn(),
  },
}));

// Mock react-native-background-fetch
jest.mock('react-native-background-fetch', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(),
    start: jest.fn(),
    stop: jest.fn(),
    status: jest.fn(),
    finish: jest.fn(),
  },
}));

// Mock react-native-geolocation-service
jest.mock('react-native-geolocation-service', () => ({
  getCurrentPosition: jest.fn(),
  requestAuthorization: jest.fn(),
}));

// Mock react-native-device-info
jest.mock('react-native-device-info', () => ({
  getUniqueId: jest.fn(),
  getSystemVersion: jest.fn(),
  getVersion: jest.fn(),
  getBuildNumber: jest.fn(),
  getBundleId: jest.fn(),
  getModel: jest.fn(),
  getBrand: jest.fn(),
  getDeviceId: jest.fn(),
  getSystemName: jest.fn(),
  getUserAgent: jest.fn(),
  getDeviceName: jest.fn(),
  getDeviceType: jest.fn(),
  isTablet: jest.fn(),
  isEmulator: jest.fn(),
  isLocationEnabled: jest.fn(),
  isCameraAvailable: jest.fn(),
  isBiometricHardwareSupported: jest.fn(),
  isFingerprintAvailable: jest.fn(),
  isFaceIdAvailable: jest.fn(),
  isPinOrFingerprintSet: jest.fn(),
  isAirplaneMode: jest.fn(),
  isBatteryCharging: jest.fn(),
  getBatteryLevel: jest.fn(),
  getPowerState: jest.fn(),
  getAvailableLocationProviders: jest.fn(),
  getCarrier: jest.fn(),
  getTotalMemory: jest.fn(),
  getUsedMemory: jest.fn(),
  getFreeDiskStorage: jest.fn(),
  getTotalDiskCapacity: jest.fn(),
  getFreeDiskStorageOld: jest.fn(),
  getTotalDiskCapacityOld: jest.fn(),
  getApiLevel: jest.fn(),
  getIpAddress: jest.fn(),
  getMacAddress: jest.fn(),
  getPhoneNumber: jest.fn(),
  getFirstInstallTime: jest.fn(),
  getLastUpdateTime: jest.fn(),
  getInstallReferrer: jest.fn(),
  getCameraPresets: jest.fn(),
  getAvailableSensors: jest.fn(),
  isSensorAvailable: jest.fn(),
  getDeviceCountry: jest.fn(),
  getDeviceLocale: jest.fn(),
  getDeviceLocaleCountryCode: jest.fn(),
  getTimezone: jest.fn(),
  isAutoDateAndTime: jest.fn(),
  isAutoTimeZone: jest.fn(),
  getSupportedAbis: jest.fn(),
  getFontScale: jest.fn(),
  isKeyboardConnected: jest.fn(),
  getBootloader: jest.fn(),
  getDevice: jest.fn(),
  getDisplay: jest.fn(),
  getFingerprint: jest.fn(),
  getHardware: jest.fn(),
  getHost: jest.fn(),
  getProduct: jest.fn(),
  getTags: jest.fn(),
  getType: jest.fn(),
  getBaseOs: jest.fn(),
  getPreviewSdkInt: jest.fn(),
  getSecurityPatch: jest.fn(),
  getCodename: jest.fn(),
  getIncremental: jest.fn(),
}));

// Mock services that use the above modules
jest.mock('~/services/location/LocationService', () => ({
  submitCheckInLocation: jest.fn().mockResolvedValue(true),
  initLocationService: jest.fn(),
  setRouteAndStopId: jest.fn(),
  startHeartbeat: jest.fn(),
  stopHeartbeat: jest.fn(),
  postLocation: jest.fn(),
  getCurrentLocationFromGeolocation: jest.fn(),
  isBackgroundLocationEnabled: jest.fn(),
  cleanupLocationService: jest.fn(),
}));

jest.mock('~/services/geofence/GeofenceService', () => ({
  getProtocolForGeofenceEntry: jest.fn().mockResolvedValue(null),
}));

jest.mock('~/services/sync/driverActions', () => ({
  DriverActions: {
    markStopArrival: jest.fn().mockResolvedValue(true),
  },
}));

jest.mock('~/hooks/useStopServices', () => ({
  useStopServices: jest.fn(() => ({
    pickupParcels: new Set(),
    deliveryParcels: new Set(),
    stopServices: new Set(),
  })),
}));

jest.mock('~/hooks/useTimer', () => ({
  useTimer: jest.fn(),
}));

jest.mock('~/utils/stops', () => ({
  buildStopTasks: jest.fn(() => []),
}));

const mockNextStop: Stop = {
  Id: '123',
  Name: 'Next Stop',
  Completed_Time__c: new Date().toISOString(),
  attributes: {
    type: '',
    url: '',
  },
  LastModifiedDate: '',
  Stop_Time_Min__c: null,
  Stop_Time_Preferred__c: null,
  Stop_Time_Max__c: null,
  ETA__c: null,
  Type__c: null,
  Address__c: null,
  Address_1__c: '',
  Address_2__c: null,
  City__c: '',
  Postal_Code__c: '',
  State__c: '',
  Status__c: STOP_STATUS.COMPLETE,
  Post_Date__c: null,
  Stop_Coordinates__Latitude__s: 0,
  Stop_Coordinates__Longitude__s: 0,
  Notes__c: null,
  Notes_Other__c: null,
  POD__c: null,
  POD_Comments__c: null,
  Customer_Reference_1__c: null,
  Customer_Reference_2__c: null,
  Pieces__c: 0,
  Summary__c: '',
  Arrival_Time__c: null,
  Geofencing_Distance__c: null,
  Location__r: {
    attributes: {
      type: '',
      url: '',
    },
    Arrival_Verification_Method__c: null,
    Name: '',
    Coordinates__Latitude__s: 0,
    Coordinates__Longitude__s: 0,
    Driver_Wait_Time_Duration__c: null,
    Geofencing_Distance__c: null,
    Business_Hours__c: null,
  },
  Coordinates__c: '',
  Wait_Time_Minutes__c: null,
  SNO_Bypass_DateTime__c: null,
  SNO_Driver_Waiting__c: false,
  SNO_Tags__c: null,
  No_Perimeter_Check_Notes__c: null,
  Pickup_Contact__c: null,
  Pickup_Comments__c: null,
  Delivery_Contact__c: null,
  Delivery_Comments__c: null,
  Driver_Initiated_Early_Departure__c: false,
  Service_Comments__c: '',
  Proof_of_Service__c: null,
};

const mockLastCompletedStop: Stop = {
  ...mockNextStop,
  Name: 'Last Stop',
  Completed_Time__c: new Date().toISOString(),
};

const mockStopList: Stop[] = [mockNextStop, mockLastCompletedStop];

const mockCurrentRoute = {
  Id: 'route-123',
  Name: 'Test Route',
  Status__c: ROUTE_STATUS.InProgress,
  attributes: { type: '', url: '' },
  LastModifiedDate: '',
  Planned_Start__c: null,
  Planned_End__c: null,
  Date__c: '',
  Start_Time__c: null,
  End_Time__c: null,
  Miles_Driven__c: 0,
  Number_of_Open_Stops__c: null,
  Number_of_Stops__c: 0,
  Number_of_Stops_Completed_Late__c: null,
  Number_of_Stops_Completed_Early__c: 0,
  Geofencing_Distance__c: null,
  Service_Status__c: null,
  Route__r: {
    attributes: { type: '', url: '' },
    Name: '',
    Timezone__c: 0 as 0,
    Geofencing_Distance__c: null,
  },
  UTC_Offset__c: null,
  Driver__c: null,
  SNO_Flow__c: true as true,
  Lock_Check_In_Order__c: false as false,
  Rapid_Closure_Warning__c: false as false,
  Hours_Run__c: 0,
  Default_Delivery_Destination__c: null,
  Survey_Complete__c: null,
  Require_End_of_Route_Survey__c: null,
};

// Add after other jest.mock calls
jest.mock('@rneui/themed', () => {
  return {
    Button: ({ onPress, children, testID, id, ...rest }: any) => {
      const { TouchableOpacity } = require('react-native');
      return (
        <TouchableOpacity onPress={onPress} testID={testID || id} {...rest}>
          {children}
        </TouchableOpacity>
      );
    },
    createTheme: () => ({
      mode: 'light',
      lightColors: { primary: '#ff0000' },
      darkColors: { primary: '#000000' },
    }),
  };
});

jest.mock('~/styles/theme', () => ({
  mode: 'light',
  lightColors: { primary: '#ff0000' },
  darkColors: { primary: '#000000' },
}));

describe('StopActionButton', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.clearAllMocks();
    (buildScenario as jest.Mock).mockResolvedValue(null);
    (ProtocolService.getApplicableProtocol as jest.Mock).mockReturnValue(null);
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.clearAllTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it('renders normal arrival button when protocol is not active', async () => {
    const { getByTestId } = render(
      <StopActionButton
        nextStop={mockNextStop}
        currentRoute={mockCurrentRoute}
        lastCompletedStop={mockLastCompletedStop}
        stopList={mockStopList}
        isPickup={false}
        isDelivery={true}
        isService={false}
      />,
    );

    await act(async () => {
      await Promise.resolve();
      jest.runAllTimers();
    });

    expect(
      getByTestId('StopActionButton.FilledButton.arrived'),
    ).toHaveTextContent(`Arrived at ${mockNextStop.Name}`);
  });

  it('renders normal arrival button when no last completed stop exists', async () => {
    const { getByTestId } = render(
      <StopActionButton
        nextStop={mockNextStop}
        currentRoute={mockCurrentRoute}
        stopList={mockStopList}
        isPickup={false}
        isDelivery={true}
        isService={false}
      />,
    );

    await act(async () => {
      await Promise.resolve();
      jest.runAllTimers();
    });

    expect(getByTestId('StopActionButton.FilledButton.arrived')).toBeTruthy();
  });

  it('navigates to SelectServiceScreen when arrival button is clicked', async () => {
    (buildStopTasks as jest.Mock).mockReturnValue([{ id: '1' }, { id: '2' }]);
    const { getByTestId, debug } = render(
      <StopActionButton
        nextStop={mockNextStop}
        currentRoute={mockCurrentRoute}
        stopList={mockStopList}
        isPickup={false}
        isDelivery={true}
        isService={false}
      />,
    );

    await act(async () => {
      await Promise.resolve();
      jest.runAllTimers();
    });

    debug(); // Log the rendered output

    await act(async () => {
      fireEvent.press(getByTestId('StopActionButton.FilledButton.arrived'));
    });

    expect(mockNavigate).toHaveBeenCalledWith('SelectServiceScreen', {
      stop: mockNextStop,
      stopList: mockStopList,
      isDelivery: true,
      isPickup: false,
      isService: false,
      title: mockNextStop.Name,
      taskList: [{ id: '1' }, { id: '2' }],
    });
  });

  it('handles stops with no name properly', async () => {
    const stopWithoutName = {
      ...mockNextStop,
      Name: '',
    };

    const { getByText } = render(
      <StopActionButton
        nextStop={stopWithoutName}
        currentRoute={mockCurrentRoute}
        stopList={mockStopList}
        isPickup={false}
        isDelivery={true}
        isService={false}
      />,
    );

    await act(async () => {
      jest.runAllTimers();
    });

    expect(getByText(`Arrived at ${en.nextStop}`)).toBeTruthy();
  });

  it('renders wait button when protocol is active', async () => {
    const protocolNextStop = { ...mockNextStop, Summary__c: 'summary-123' };
    (buildScenario as jest.Mock).mockResolvedValue({ id: 'scenario1' });
    (ProtocolService.getApplicableProtocol as jest.Mock).mockReturnValue({
      id: 'protocol1',
      executionCriteria: {
        all: [{ customOperator: 'isWithinTimeSince', value: 600 }],
      },
    });
    const { getByTestId } = render(
      <StopActionButton
        nextStop={protocolNextStop}
        currentRoute={mockCurrentRoute}
        lastCompletedStop={mockLastCompletedStop}
        stopList={mockStopList}
        isPickup={false}
        isDelivery={true}
        isService={false}
      />,
    );
    await act(async () => {
      await Promise.resolve();
      jest.runAllTimers();
    });
    await waitFor(() =>
      expect(getByTestId('StopActionButton.FilledButton.wait')).toBeTruthy(),
    );
  });

  it('wait button click launches protocol flow', async () => {
    const protocolNextStop = { ...mockNextStop, Summary__c: 'summary-123' };
    (buildScenario as jest.Mock).mockResolvedValue({ id: 'scenario1' });
    (ProtocolService.getApplicableProtocol as jest.Mock).mockReturnValue({
      id: 'protocol1',
      executionCriteria: {
        all: [{ customOperator: 'isWithinTimeSince', value: 600 }],
      },
    });
    const { getByTestId } = render(
      <StopActionButton
        nextStop={protocolNextStop}
        currentRoute={mockCurrentRoute}
        lastCompletedStop={mockLastCompletedStop}
        stopList={mockStopList}
        isPickup={false}
        isDelivery={true}
        isService={false}
      />,
    );
    await act(async () => {
      await Promise.resolve();
      jest.runAllTimers();
    });
    await waitFor(() =>
      expect(getByTestId('StopActionButton.FilledButton.wait')).toBeTruthy(),
    );
    await act(async () => {
      fireEvent.press(getByTestId('StopActionButton.FilledButton.wait'));
    });
    expect(mockNavigate).toHaveBeenCalledWith('ProtocolEngineStack', {
      protocol: expect.any(Object),
      scenario: expect.any(Object),
    });
  });

  it('wait button shows correct remaining time', async () => {
    const protocolNextStop = { ...mockNextStop, Summary__c: 'summary-123' };
    (buildScenario as jest.Mock).mockResolvedValue({ id: 'scenario1' });
    (ProtocolService.getApplicableProtocol as jest.Mock).mockReturnValue({
      id: 'protocol1',
      executionCriteria: {
        all: [{ customOperator: 'isWithinTimeSince', value: 600 }],
      },
    });
    const { getByTestId, getByText } = render(
      <StopActionButton
        nextStop={protocolNextStop}
        currentRoute={mockCurrentRoute}
        lastCompletedStop={mockLastCompletedStop}
        stopList={mockStopList}
        isPickup={false}
        isDelivery={true}
        isService={false}
      />,
    );
    await act(async () => {
      await Promise.resolve();
      jest.runAllTimers();
    });
    await waitFor(() =>
      expect(getByTestId('StopActionButton.FilledButton.wait')).toBeTruthy(),
    );
    // Check for timer text in the button
    expect(getByText(/\d{2}:\d{2}/)).toBeTruthy();
  });

  it('handles error in onArrivedStopClicked gracefully', async () => {
    (buildStopTasks as jest.Mock).mockReturnValue([{ id: '1' }]);
    (buildScenario as jest.Mock).mockResolvedValue(null);
    (ProtocolService.getApplicableProtocol as jest.Mock).mockReturnValue(null);
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const { getByTestId } = render(
      <StopActionButton
        nextStop={mockNextStop}
        currentRoute={mockCurrentRoute}
        lastCompletedStop={mockLastCompletedStop}
        stopList={mockStopList}
        isPickup={false}
        isDelivery={true}
        isService={false}
      />,
    );
    await act(async () => {
      await Promise.resolve();
      jest.runAllTimers();
    });
    // Simulate error in handleStopArrival
    jest
      .spyOn(
        require('~/services/sync/driverActions').DriverActions,
        'markStopArrival',
      )
      .mockRejectedValue(new Error('fail'));
    await act(async () => {
      fireEvent.press(getByTestId('StopActionButton.FilledButton.arrived'));
    });
    expect(errorSpy).toHaveBeenCalled();
    errorSpy.mockRestore();
  });

  it('renders correctly with missing lastCompletedStop', async () => {
    const { getByTestId } = render(
      <StopActionButton
        nextStop={mockNextStop}
        currentRoute={mockCurrentRoute}
        lastCompletedStop={null}
        stopList={mockStopList}
        isPickup={false}
        isDelivery={true}
        isService={false}
      />,
    );
    await act(async () => {
      await Promise.resolve();
      jest.runAllTimers();
    });
    expect(getByTestId('StopActionButton.FilledButton.arrived')).toBeTruthy();
  });
});
