import Realm from 'realm';

export const ParcelSchema: Realm.ObjectSchema = {
  name: 'Parcel__c',
  primaryKey: 'Id',
  properties: {
    Id: 'string',
    Requires_Signature__c: {
      type: 'bool',
      default: false,
    },
    Reference_Required__c: {
      type: 'bool',
      default: false,
    },
    Parcel_Type_Name__c: 'string',
    Parcel_Type_Definition__c: 'string?',
    Pickup__c: 'string?',
    Delivery__c: 'string',
    Quantity__c: 'int',
    Signature__c: 'string?',
    Signature_Comments__c: 'string?',
    Signature_Statuses__c: 'string?',
    Quantity_Expected__c: 'int?',
    Dropoff_Quantity__c: 'int?',
    Comments__c: 'string?',
    Reference__c: 'string?',
  },
};

export const ParcelTypeSchema: Realm.ObjectSchema = {
  name: 'Parcel_Type__c',
  primaryKey: 'Id',
  properties: {
    Id: 'string',
    Name: 'string',
  },
};
