export const RouteSummarySchema = {
  name: 'Route_Summary__c',
  primaryKey: 'Id',
  properties: {
    Id: 'string',
    attributes: 'string?',
    LastModifiedDate: 'string?',
    Name: 'string?',
    Planned_Start__c: 'string?',
    Planned_End__c: 'string?',
    Date__c: 'string?',
    Start_Time__c: 'string?',
    End_Time__c: 'string?',
    Number_of_Open_Stops__c: 'string?',
    Number_of_Stops__c: 'int?',
    Number_of_Stops_Completed_Late__c: 'string?',
    Number_of_Stops_Completed_Early__c: 'int?',
    Geofencing_Distance__c: 'int?',
    Status__c: 'string?',
    Service_Status__c: 'string?',
    Route__r: 'string?',
    UTC_Offset__c: 'double?',
    Driver__c: 'string?',
    SNO_Flow__c: 'bool?',
    Lock_Check_In_Order__c: 'bool?',
    Rapid_Closure_Warning__c: 'bool?',
    Hours_Run__c: 'double?',
    Default_Delivery_Destination__c: 'bool?',
    Survey_Complete__c: 'bool?',
    Require_End_of_Route_Survey__c: 'bool?',
    High_Temperature__c: 'int?',
  },
};
