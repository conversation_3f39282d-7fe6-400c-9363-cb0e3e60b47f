import Realm from 'realm';

export const ImageSchema: Realm.ObjectSchema = {
  name: 'Image',
  primaryKey: 'Id',
  properties: {
    Id: 'string',
    Title: 'string',
    VersionData: 'string',
    PathOnClient: 'string',
    // linked entities
    StopId: 'string?',
    RouteSummaryId: 'string',
    ParcelId: 'string?',
    Coordinates__Latitude__s: 'double?',
    Coordinates__Longitude__s: 'double?',
    // sync properties
    isSyncReady: {
      type: 'bool',
      default: false,
    },
    isSynced: {
      type: 'bool',
      default: false,
    },
    retryCount: {
      type: 'int',
      default: 0,
    },
    createdAt: {
      type: 'date',
      default: new Date(),
    },
    updatedAt: {
      type: 'date',
      default: new Date(),
    },
  },
};
