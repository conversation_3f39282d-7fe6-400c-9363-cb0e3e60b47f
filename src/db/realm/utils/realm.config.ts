import Realm from 'realm';
import { SCHEMAS } from '~/db/realm/schemas';
import { migrateRealm } from '~/db/realm/utils/migration';

let realmInstance: Realm | null = null;
let isInitializing = false;

export async function getRealmInstance(): Promise<Realm> {
  // Prevent multiple simultaneous initializations
  if (isInitializing) {
    // Wait for the current initialization to complete
    while (isInitializing) {
      await new Promise(resolve => setTimeout(resolve, 300));
    }
    return realmInstance!;
  }

  if (!realmInstance || realmInstance.isClosed) {
    isInitializing = true;
    try {
      realmInstance = await Realm.open({
        schema: SCHEMAS,
        schemaVersion: 4,
        onMigration: migrateRealm,
      });

      if (__DEV__) {
        const realmFileLocation = realmInstance.path;
        console.info(
          `realm.config.ts: getRealmInstance(): Realm file is located at: ${realmFileLocation}`,
        );
      }
    } catch (error) {
      console.error('Error initializing Realm:', error);
      throw error;
    } finally {
      isInitializing = false;
    }
  }

  return realmInstance;
}

export function closeRealmInstance() {
  if (realmInstance && !realmInstance.isClosed) {
    try {
      realmInstance.close();
      console.info('Realm instance closed successfully');
    } catch (error) {
      console.warn('Error closing Realm instance:', error);
    } finally {
      realmInstance = null;
    }
  }
}
