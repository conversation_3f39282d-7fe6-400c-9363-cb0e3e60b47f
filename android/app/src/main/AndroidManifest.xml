<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

  <uses-feature android:name="android.hardware.camera" android:required="false"/>
  <uses-feature android:name="android.hardware.camera.any" android:required="false" />
  <uses-permission android:name="android.permission.INTERNET"/>
  <uses-permission android:name="android.permission.CAMERA"/>
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
  <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
  <uses-permission android:minSdkVersion="34" android:name="android.permission.USE_EXACT_ALARM" />
  <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />


  <!-- Required: declare external URL schemes for Linking.canOpenURL support on Android 11+ -->
  <queries>
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="https" android:host="www.gorapidmedical.com"/>
    </intent>
  </queries>

  <application 
      android:name=".MainApplication" 
      android:label="@string/APP_NAME" 
      android:icon="@mipmap/ic_launcher" 
      android:allowBackup="false" 
      android:theme="@style/AppTheme" 
      android:supportsRtl="true"
      tools:replace="android:label">
      
    <activity 
        android:name=".MainActivity" 
        android:label="@string/APP_NAME" 
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" 
        android:launchMode="singleTask" 
        android:windowSoftInputMode="adjustResize" 
        android:exported="true" 
        android:screenOrientation="portrait">
      <intent-filter>
        <action android:name="android.intent.action.MAIN"/>
        <category android:name="android.intent.category.LAUNCHER"/>
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:scheme="oauth"/>
      </intent-filter>
    </activity>
 
    <meta-data android:name="io.sentry.auto-init" android:value="false" />
    <!-- Required: set your sentry.io project identifier (DSN) -->
    <meta-data android:name="io.sentry.dsn" android:value="https://<EMAIL>/4507935366447104" />
    <!-- enable the performance API by setting a sample-rate, adjust in production env -->
    <meta-data android:name="io.sentry.traces.sample-rate" android:value="1.0" />
    <meta-data android:name="io.sentry.environment" android:value="prod" tools:replace="android:value" />

    <!-- react-native-background-geolocation licence -->
    <meta-data android:name="com.transistorsoft.locationmanager.license" android:value="b1bbfeb852499529162dea2c803898eb2c2f957fe5463115538bf62ba171d031" />
    <meta-data android:name="MAPBOX_ACCESS_TOKEN"
				   android:value="pk.eyJ1IjoiamFjb2ItcmFwaWQiLCJhIjoiY2xoZ3ptYmQ2MGowOTNmbDk3cHNuanM2diJ9.GnufQhp0Zpn7S_oO9Ck5mw" />
  </application>
</manifest>
